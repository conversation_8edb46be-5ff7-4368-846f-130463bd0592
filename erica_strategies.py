from dataclasses import dataclass
from typing import Optional, List, Dict, Any


@dataclass
class StrategyParameters:
    min_premium: float = 0.25
    max_dte: int = 60
    target_delta: float = 0.25


@dataclass
class StrategyDecision:
    name: str
    suitable: bool
    confidence: float
    rationale: str
    details: Dict[str, Any]


class Strategy:
    name: str = "base"

    def __init__(self, params: StrategyParameters):
        self.params = params

    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        raise NotImplementedError


class CoveredCall(Strategy):
    name = "covered_call"

    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        # Requirements: own 100 shares, 30-45 DTE, ~0.30 delta, min premium
        owns_shares = context.get("owns_shares", False)
        ivr = context.get("ivr")
        chain = context.get("options_chain")
        suitable = bool(owns_shares)
        rationale = []
        details: Dict[str, Any] = {"target_delta": 0.30, "dte_window": (30, 45)}
        if not owns_shares:
            rationale.append("Requires 100 shares in inventory")
        else:
            rationale.append("Inventory available for covered call")
        if ivr is not None and ivr > 0.5:
            rationale.append("High IVR favors premium selling")
        # Options selection
        try:
            from options_utils import normalize_chain, filter_by_exp_window, nearest_by_delta
            opts = normalize_chain(chain)
            calls = [o for o in opts if o["type"] == "call"]
            window = filter_by_exp_window(calls, 30, 45)
            sel = nearest_by_delta(window, 0.30)
            if sel:
                details.update({
                    "short_call": {
                        "strike": sel["strike"], "exp": sel["expiration_str"],
                        "mid": sel["mid"], "delta": sel.get("delta"), "theta": sel.get("theta"), "iv": sel.get("iv"),
                    }
                })
                rationale.append("Selected 0.30Δ short call using live chain")
        except Exception:
            pass
        conf = 0.6 if suitable else 0.0
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)


class PutCreditSpread(Strategy):
    name = "put_credit_spread"

    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        # Setup: bullish/neutral, sell put at 0.15-0.20 delta, buy 5-10 strikes lower, 30-45 DTE
        trend = context.get("trend")
        ivr = context.get("ivr")
        rsi = context.get("rsi")
        chain = context.get("options_chain")

        # Enhanced suitability logic
        suitable = False
        rationale = [f"Trend {trend}"]

        # Primary: Neutral/bull trend with good premium
        if trend in ("bull", "neutral") and (ivr is None or ivr >= 0.3):
            suitable = True
            rationale.append("Bullish/neutral bias supports put selling")

        # Boost confidence for high IVR
        if ivr is not None and ivr >= 0.4:
            rationale.append(f"High IVR {ivr:.2f} supports premium selling")

        details: Dict[str, Any] = {"short_delta": 0.18, "width": 5, "dte_window": (30, 45)}
        try:
            from options_utils import normalize_chain, filter_by_exp_window, nearest_by_delta, find_spread_long_leg
            opts = normalize_chain(chain)
            puts = [o for o in opts if o["type"] == "put"]
            window = filter_by_exp_window(puts, 30, 45)
            short_put = nearest_by_delta(window, 0.18)
            if short_put:
                long_put = find_spread_long_leg(window, short_put, width=5, same_exp=True)
                details["short_put"] = {"strike": short_put["strike"], "exp": short_put["expiration_str"],
                                        "mid": short_put["mid"], "delta": short_put.get("delta"), "iv": short_put.get("iv")}
                if long_put:
                    details["long_put"] = {"strike": long_put["strike"], "exp": long_put["expiration_str"],
                                           "mid": long_put["mid"], "delta": long_put.get("delta"), "iv": long_put.get("iv")}
                    # Estimates per contract
                    try:
                        net_credit = (short_put.get("mid") or 0.0) - (long_put.get("mid") or 0.0)
                        width = abs(float(short_put["strike"]) - float(long_put["strike"]))
                        max_loss = max(0.0, width - net_credit) * 100.0
                        max_profit = max(0.0, net_credit) * 100.0
                        win_prob = max(0.0, min(1.0, 1.0 - abs(float(short_put.get("delta") or 0.18))))
                        ev = win_prob * max_profit - (1.0 - win_prob) * max_loss
                        details["estimates"] = {
                            "net_credit": float(net_credit),
                            "width": float(width),
                            "max_profit": float(max_profit),
                            "max_loss": float(max_loss),
                            "ev_naive": float(ev),
                        }
                    except Exception:
                        pass
                    rationale.append("Built PCS with width≈5 using live chain")
        except Exception:
            pass

        # Adjust confidence based on conditions
        if suitable:
            conf = 0.75 if (ivr is not None and ivr >= 0.4) else 0.65
        else:
            conf = 0.2
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)


class LEAPS(Strategy):
    name = "leaps"

    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        # Buy calls 12+ months, 0.70-0.80 delta, use in bull trends OR oversold conditions with low IVR
        trend = context.get("trend")
        ivr = context.get("ivr")
        rsi = context.get("rsi")

        # Enhanced suitability logic
        suitable = False
        rationale = [f"Trend {trend}"]

        # Strong bullish case
        if trend == "bull" and (ivr is None or ivr < 0.5):
            suitable = True
            rationale.append("Bull trend with reasonable IV")

        # Oversold bounce play with low IV
        elif rsi is not None and rsi < 30 and (ivr is None or ivr < 0.3):
            suitable = True
            rationale.append(f"Oversold RSI {rsi:.1f} + low IVR favors long calls")

        # Low IV directional play
        elif ivr is not None and ivr < 0.2 and trend != "bear":
            suitable = True
            rationale.append("Very low IVR makes long calls attractive")

        if ivr is not None and ivr < 0.3:
            rationale.append("Low IVR favors directional long calls")

        details = {"delta": 0.75, "min_dte": 365}
        conf = 0.75 if suitable else 0.1
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)


class CashSecuredPut(Strategy):
    name = "cash_secured_put"

    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        # Use when oversold/bullish reversal expected; sell put to acquire shares
        rsi = context.get("rsi")
        trend = context.get("trend")
        ivr = context.get("ivr")

        suitable = False
        rationale = []

        # Primary: Oversold conditions
        if rsi is not None and rsi < 35:
            suitable = True
            rationale.append(f"Oversold RSI {rsi:.1f} suggests reversal opportunity")

        # Secondary: Neutral/bull trend with decent premium
        elif trend in ("neutral", "bull") and ivr is not None and ivr > 0.3:
            suitable = True
            rationale.append(f"Trend {trend} + IVR {ivr:.2f} supports premium collection")

        if not rationale:
            rationale.append(f"RSI={rsi}, Trend={trend}")

        details = {"target_delta": 0.20, "dte_window": (30, 45)}
        conf = 0.7 if suitable else 0.15
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)


class PoorMansCoveredCall(Strategy):
    name = "pmcc"

    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        """
        Poor Man's Covered Call (Diagonal):
        - Buy a long-dated LEAPS call (>= 365 DTE), with configurable target delta (e.g., 0.50–0.70)
        - Sell a shorter-dated call against it on up days, with conservative strike
        - Management uses alarms and rolling rules
        Note: This implementation scores suitability using available context only (trend/ivr);
        selection of exact strikes requires options chain integration.
        """
        trend = context.get("trend")
        ivr = context.get("ivr")
        # Favor neutral-to-bullish with not-too-high IV to keep LEAPS affordable
        suitable = trend in ("bull", "neutral")
        rationale = [f"Trend {trend}"]
        if ivr is not None:
            if ivr < 0.5:
                rationale.append("LEAPS relatively affordable (IVR not elevated)")
            else:
                rationale.append("High IVR: short-call premium attractive; verify LEAPS cost")
        details = {
            "leaps_min_dte": 365,
            "leaps_target_delta_range": (0.50, 0.70),
            "short_call_dte_window": (21, 45),
            "alarm_rule": "Set alert $1–$2 below short strike; re-arm on rolls",
            "management": "Roll up/out near strike; optionally convert to covered call",
        }
        conf = 0.6 if suitable else 0.2
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)


class CalendarSpread(Strategy):
    name = "calendar_spread"
    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        trend = context.get("trend")
        ivr = context.get("ivr")
        rsi = context.get("rsi")
        chain = context.get("options_chain")

        details: Dict[str, Any] = {"front_dte": (21, 45), "back_dte": (60, 120), "strike_bias": "ATM"}
        rationale = []
        suitable = False

        # Primary: Neutral trend (ideal for calendars)
        if trend == "neutral":
            suitable = True
            rationale.append("Neutral trend ideal for calendar spreads")

        # Secondary: High IVR (term structure play)
        elif ivr is not None and ivr >= 0.4:
            suitable = True
            rationale.append(f"High IVR {ivr:.2f} supports calendar term structure")

        # Tertiary: Sideways/consolidation after trend (RSI 35-65)
        elif rsi is not None and 35 <= rsi <= 65 and trend != "bull":
            suitable = True
            rationale.append(f"RSI {rsi:.1f} suggests consolidation phase")

        try:
            from options_utils import normalize_chain, filter_by_exp_window, nearest_atm
            from yahoo_options import bs_price, years_to
            opts = normalize_chain(chain)
            opt_type = "put" if trend == "bear" else "call"
            legs = [o for o in opts if o["type"] == opt_type]
            # term structure via ATM IVs
            front = filter_by_exp_window(legs, 21, 45)
            back = filter_by_exp_window(legs, 60, 120)
            spot = context.get("price")
            f_atm = nearest_atm(front, spot)
            b_atm = nearest_atm(back, spot)
            if f_atm and b_atm and f_atm.get("iv") and b_atm.get("iv"):
                details["term_structure"] = {"front_iv": f_atm["iv"], "back_iv": b_atm["iv"], "type": opt_type}
                if f_atm["iv"] >= b_atm["iv"]:
                    rationale.append("Front IV >= Back IV: favorable calendar term structure")
                    suitable = True
            # Select ATM strikes per exp windows and compute estimates
            if f_atm and b_atm:
                details["front_short"] = {"type": opt_type, "strike": f_atm["strike"], "exp": f_atm["expiration_str"],
                                           "mid": f_atm["mid"], "iv": f_atm.get("iv"), "theta": f_atm.get("theta")}
                details["back_long"] = {"type": opt_type, "strike": b_atm["strike"], "exp": b_atm["expiration_str"],
                                          "mid": b_atm["mid"], "iv": b_atm.get("iv"), "theta": b_atm.get("theta")}
                rationale.append("Selected ATM calendar legs using live chain")
                # debit and est. max profit at front expiry with spot ~ strike
                try:
                    debit = (b_atm.get("mid") or 0.0) - (f_atm.get("mid") or 0.0)
                    T_front = years_to(b_atm["expiration"]) - years_to(f_atm["expiration"]) if b_atm.get("expiration") and f_atm.get("expiration") else None
                    est = None
                    if T_front and T_front > 0:
                        K = float(f_atm["strike"])
                        iv_back = b_atm.get("iv")
                        # If IV missing from Yahoo for back leg, infer IV from mid price using solver
                        if (iv_back is None or iv_back == 0) and b_atm.get("mid") is not None:
                            iv_back = implied_vol(spot=K, strike=K, r=0.04, q=0.0, T=T_front, opt_type=opt_type, price=float(b_atm["mid"]))
                        if iv_back is not None and iv_back > 0:
                            est = bs_price(K, K, 0.04, 0.0, float(iv_back), T_front, opt_type)
                    est_profit = (est - debit) if (est is not None and debit is not None) else None
                    # naive theta/day estimate: |short theta| - |long theta|
                    theta_s = abs(float(f_atm.get("theta") or 0.0))
                    theta_l = abs(float(b_atm.get("theta") or 0.0))
                    # If theta missing, approximate from BS greeks using inferred IVs
                    if (theta_s == 0.0 or theta_l == 0.0) and K and T_front and T_front > 0:
                        from yahoo_options import _bs_greeks as _g
                        iv_front = f_atm.get("iv")
                        if (iv_front is None or iv_front == 0) and f_atm.get("mid") is not None:
                            iv_front = implied_vol(spot=K, strike=K, r=0.04, q=0.0, T=years_to(f_atm["expiration"]), opt_type=opt_type, price=float(f_atm["mid"]))
                        if (theta_s == 0.0) and iv_front:
                            g = _g(K, K, 0.04, 0.0, float(iv_front), years_to(f_atm["expiration"]), opt_type)
                            theta_s = abs(float(g.get("theta") or 0.0))
                        if (theta_l == 0.0) and iv_back:
                            g = _g(K, K, 0.04, 0.0, float(iv_back), years_to(b_atm["expiration"]), opt_type)
                            theta_l = abs(float(g.get("theta") or 0.0))
                    theta_day = theta_s - theta_l
                    details["estimates"] = {
                        "debit": float(debit) if debit is not None else None,
                        "est_max_profit": float(est_profit) if est_profit is not None else None,
                        "exp_theta_per_day": float(theta_day),
                    }
                except Exception:
                    pass
        except Exception:
            pass
        # Management/roll rules
        details["management"] = {
            "profit_targets": "Take profits 25-50% of max; monitor time decay",
            "time_exit": "Exit before near-term expiry or when theta decay slows",
            "vol_response": "If vol expands unfavorably, consider closing or rolling out",
        }
        conf = 0.6 if suitable else 0.2
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)




class BearCallSpread(Strategy):
    name = "bear_call_spread"
    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        trend = context.get("trend")
        rsi = context.get("rsi")
        ivr = context.get("ivr")
        chain = context.get("options_chain")

        details: Dict[str, Any] = {"short_delta": 0.20, "width": 5, "dte_window": (30, 45)}
        suitable = False
        rationale = []

        # Primary: Bear trend
        if trend == "bear":
            suitable = True
            rationale.append(f"Bear trend supports call selling")

        # Secondary: Overbought conditions
        elif rsi is not None and rsi > 70:
            suitable = True
            rationale.append(f"Overbought RSI {rsi:.1f} suggests downward pressure")

        # Tertiary: High IVR in neutral market
        elif trend == "neutral" and ivr is not None and ivr > 0.5:
            suitable = True
            rationale.append(f"High IVR {ivr:.2f} in neutral market favors premium selling")

        if not rationale:
            rationale = [f"Trend {trend}", f"RSI={rsi}"]

        try:
            from options_utils import normalize_chain, filter_by_exp_window, nearest_by_delta, find_spread_long_leg
            opts = normalize_chain(chain)
            calls = [o for o in opts if o["type"] == "call"]
            window = filter_by_exp_window(calls, 30, 45)
            short_call = nearest_by_delta(window, 0.20)
            if short_call:
                long_call = find_spread_long_leg(window, short_call, width=5, same_exp=True)
                details["short_call"] = {"strike": short_call["strike"], "exp": short_call["expiration_str"],
                                          "mid": short_call["mid"], "delta": short_call.get("delta"), "iv": short_call.get("iv")}
                if long_call:
                    details["long_call"] = {"strike": long_call["strike"], "exp": long_call["expiration_str"],
                                             "mid": long_call["mid"], "delta": long_call.get("delta"), "iv": long_call.get("iv")}
                    rationale.append("Constructed BCS width≈5 using live chain")
        except Exception:
            pass

        # Higher confidence for clear bear signals
        if suitable:
            if trend == "bear" and (rsi is None or rsi < 50):
                conf = 0.8  # Strong bear signal
            elif rsi is not None and rsi > 75:
                conf = 0.75  # Very overbought
            else:
                conf = 0.65
        else:
            conf = 0.15
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)


class RichWomansCoveredCall(Strategy):
    name = "rwcc"
    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        trend = context.get("trend"); chain = context.get("options_chain")
        suitable = trend in ("bull", "neutral")
        details: Dict[str, Any] = {"long_call_min_dte": 180, "short_call_dte_window": (21, 45), "target_delta": 0.25}
        rationale = [f"Trend {trend}"]
        try:
            from options_utils import normalize_chain, filter_by_exp_window, nearest_by_delta
            opts = normalize_chain(chain)
            calls = [o for o in opts if o["type"] == "call"]
            long_leg = filter_by_exp_window(calls, 180, 540)
            short_leg = filter_by_exp_window(calls, 21, 45)
            long_call = nearest_by_delta(long_leg, 0.60)
            short_call = nearest_by_delta(short_leg, 0.25)
            if long_call:
                details["long_call"] = {"strike": long_call["strike"], "exp": long_call["expiration_str"],
                                         "mid": long_call["mid"], "delta": long_call.get("delta"), "iv": long_call.get("iv")}
            if short_call:
                details["short_call"] = {"strike": short_call["strike"], "exp": short_call["expiration_str"],
                                          "mid": short_call["mid"], "delta": short_call.get("delta"), "iv": short_call.get("iv")}
                rationale.append("RWCC legs selected via live chain")
        except Exception:
            pass
        conf = 0.58 if suitable else 0.2
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)



class WheelStrategy(Strategy):
    name = "wheel"
    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        trend = context.get("trend"); chain = context.get("options_chain")
        suitable = trend in ("bull", "neutral")
        details: Dict[str, Any] = {"csp_delta": 0.20, "csp_dte": (30, 45), "cc_delta": 0.30, "cc_dte": (30, 45)}
        rationale = [f"Trend {trend}", "Wheel builds positions via CSP then CC"]
        try:
            from options_utils import normalize_chain, filter_by_exp_window, nearest_by_delta
            opts = normalize_chain(chain)
            puts = [o for o in opts if o["type"] == "put"]
            calls = [o for o in opts if o["type"] == "call"]
            short_put = nearest_by_delta(filter_by_exp_window(puts, 30, 45), 0.20)
            short_call = nearest_by_delta(filter_by_exp_window(calls, 30, 45), 0.30)
            if short_put:
                details["short_put"] = {"strike": short_put["strike"], "exp": short_put["expiration_str"], "mid": short_put["mid"], "delta": short_put.get("delta")}
            if short_call:
                details["short_call"] = {"strike": short_call["strike"], "exp": short_call["expiration_str"], "mid": short_call["mid"], "delta": short_call.get("delta")}
        except Exception:
            pass
        conf = 0.56 if suitable else 0.2
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)


class OversoldBounce(Strategy):
    name = "oversold_bounce"

    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        """Aggressive strategy for severely oversold conditions"""
        rsi = context.get("rsi")
        trend = context.get("trend")
        ivr = context.get("ivr")

        suitable = False
        rationale = []

        # Very oversold conditions (RSI < 25)
        if rsi is not None and rsi < 25:
            suitable = True
            rationale.append(f"Severely oversold RSI {rsi:.1f} suggests bounce opportunity")

            # Prefer LEAPS if low IV, CSP if high IV
            if ivr is not None and ivr < 0.3:
                rationale.append("Low IVR favors long calls for bounce")
            else:
                rationale.append("Elevated IVR favors selling puts for bounce")

        details = {"rsi_threshold": 25, "strategy_type": "bounce_play"}
        conf = 0.8 if suitable else 0.0
        return StrategyDecision(self.name, suitable, conf, "; ".join(rationale), details)


class RollManagement(Strategy):
    name = "roll_management"
    def evaluate(self, context: Dict[str, Any]) -> StrategyDecision:
        rationale = ["Management overlay: use with CC/Spreads/Calendars per PDF"]
        details = {"rules": [
            "Close at ~50% profit or by 21 DTE",
            "Roll when short strike tested: move out in time and adjust strikes",
            "Use alerts $1–$2 below short strike; re-arm on rolls",
        ]}
        return StrategyDecision(self.name, False, 0.05, "; ".join(rationale), details)


ALL_STRATEGIES = [
    OversoldBounce,      # Check first for extreme conditions
    CoveredCall,
    PutCreditSpread,
    LEAPS,
    CashSecuredPut,
    PoorMansCoveredCall,
    CalendarSpread,
    BearCallSpread,
    RichWomansCoveredCall,
    WheelStrategy,
    RollManagement,
]

