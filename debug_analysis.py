#!/usr/bin/env python3
"""Debug script to test individual stock analysis and identify homogeneous results issue"""

import os
from daily_outline import FMPClient, detect_market_env
from strategy_engine import StrategyEngine
from erica_strategies import StrategyParameters
from risk_management import RiskParameters
from daily_outline import fetch_extras
from yahoo_options import get_options_chain_yf

# Set API key
os.environ['FMP_API_KEY'] = 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'

def debug_stock_analysis():
    symbols = ['AAPL', 'NVDA', 'GOOGL', 'AMZN', 'AMD']
    fmp = FMPClient()
    market_env, vol_regime = detect_market_env(fmp)
    engine = StrategyEngine(StrategyParameters(), RiskParameters())
    
    print(f"Market Environment: {market_env}, Vol Regime: {vol_regime}")
    print("=" * 80)
    
    for symbol in symbols:
        print(f"\n{symbol} Analysis:")
        print("-" * 40)
        
        # Fetch data
        quote = fmp.get_quote(symbol) or {}
        hist = fmp.get_history(symbol, timeseries=260)
        news = fmp.get_news([symbol], limit=5)
        extras = fetch_extras(fmp, symbol)
        spot = quote.get('price') or quote.get('c')
        extras['options_chain'] = get_options_chain_yf(symbol, spot_price=spot, max_expirations=5)
        
        # Build context
        ctx = engine.build_context(symbol, quote, hist, news, extras)
        
        # Print technical indicators
        print(f"Price: ${ctx.price:.2f}")
        rsi_str = f"{ctx.rsi:.1f}" if ctx.rsi is not None else "None"
        ivr_str = f"{ctx.ivr:.3f}" if ctx.ivr is not None else "None"
        print(f"RSI: {rsi_str}")
        print(f"Trend: {ctx.trend}")
        print(f"IVR: {ivr_str}")
        
        # Run strategy analysis
        result = engine.analyze_ticker(ctx, account_size=100000.0, vol_regime=vol_regime, owns_shares=False, market_env=market_env)
        
        # Print strategy results
        pd = result.get("primary_decision")
        score = result.get("primary_score", 0.0)
        print(f"Primary Strategy: {pd.name if pd else 'None'}")
        print(f"Confidence Score: {score:.3f}")
        print(f"Rationale: {pd.rationale if pd else 'None'}")
        
        # Print all strategy scores for comparison
        all_scored = result.get("all_scored", [])
        print("All Strategy Scores:")
        for s in all_scored[:5]:  # Top 5
            decision = s.get("decision")
            composite = s.get("composite_score", 0)
            print(f"  {decision.name}: {composite:.3f} ({'suitable' if decision.suitable else 'not suitable'})")

if __name__ == "__main__":
    debug_stock_analysis()
