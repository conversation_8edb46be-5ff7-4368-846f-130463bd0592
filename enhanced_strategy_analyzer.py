"""
Enhanced Strategy Analyzer - Detailed Stock Classification System
Based on <PERSON>'s documented investment strategies with comprehensive analysis output

This module provides detailed strategy-based stock classification that includes:
1. Specific strategy matching with exact criteria validation
2. Detailed explanations of why each stock fits a particular strategy
3. Key catalysts driving the investment thesis
4. Financial analyst projections and reviews
5. Supporting data and metrics that justify the classification
6. Integration with existing Erica methodology implementation

Date: August 18, 2025
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import logging

# Import existing system components
from intelligent_strategy_engine import IntelligentStrategyEngine, StrategyOfTheDay
from market_analysis_engine import MarketAnalysisEngine, MarketFactors, StockSpecificFactors
from strategy_decision_tree import StrategyDecisionTree, StrategyRecommendation
from erica_strategy_engine import EricaStrategyEngine, EricaSignals, MarketBias, EricaTradeSetup
from ai_analysis_engine import AIAnalysisEngine, AIAnalysisResult, AnalysisType
from daily_outline import StrategyType
from enhanced_strategies import STOCK_RULES

class AnalysisDepth(Enum):
    BASIC = "basic"
    DETAILED = "detailed"
    COMPREHENSIVE = "comprehensive"

class ConfidenceLevel(Enum):
    VERY_HIGH = "very_high"  # 90%+
    HIGH = "high"           # 75-89%
    MODERATE = "moderate"   # 60-74%
    LOW = "low"            # 40-59%
    VERY_LOW = "very_low"  # <40%

@dataclass
class StrategyCriteriaMatch:
    """Detailed matching of strategy criteria"""
    criterion_name: str
    required_value: Any
    actual_value: Any
    is_met: bool
    score: float  # 0-1
    importance: str  # "critical", "important", "moderate", "minor"
    explanation: str

@dataclass
class CatalystAnalysis:
    """Analysis of key catalysts driving the investment thesis"""
    catalyst_type: str  # "earnings", "technical", "fundamental", "market", "news"
    description: str
    impact_level: str  # "high", "medium", "low"
    timeframe: str  # "immediate", "short_term", "medium_term", "long_term"
    probability: float  # 0-1
    supporting_evidence: List[str]

@dataclass
class FinancialProjections:
    """Financial analyst projections and reviews"""
    price_target_mean: Optional[float]
    price_target_high: Optional[float]
    price_target_low: Optional[float]
    analyst_rating: Optional[str]  # "Strong Buy", "Buy", "Hold", "Sell", "Strong Sell"
    eps_estimate_current: Optional[float]
    eps_estimate_next: Optional[float]
    revenue_growth_estimate: Optional[float]
    analyst_count: int
    upgrade_downgrade_trend: str
    key_analyst_notes: List[str]

@dataclass
class SupportingMetrics:
    """Supporting data and metrics that justify the classification"""
    technical_indicators: Dict[str, Any]
    fundamental_metrics: Dict[str, Any]
    options_metrics: Dict[str, Any]
    market_metrics: Dict[str, Any]
    risk_metrics: Dict[str, Any]
    comparative_analysis: Dict[str, Any]

@dataclass
class DetailedStrategyAnalysis:
    """Comprehensive strategy analysis with detailed explanations"""
    symbol: str
    analysis_timestamp: datetime
    
    # Primary Strategy Classification
    primary_strategy: StrategyType
    strategy_confidence: ConfidenceLevel
    strategy_score: float  # 0-100
    
    # Detailed Strategy Matching
    criteria_matches: List[StrategyCriteriaMatch]
    criteria_summary: str
    why_this_strategy: str
    
    # Investment Thesis
    key_catalysts: List[CatalystAnalysis]
    investment_thesis: str
    risk_factors: List[str]
    
    # Financial Analysis
    financial_projections: FinancialProjections
    analyst_sentiment: str
    
    # Supporting Data
    supporting_metrics: SupportingMetrics
    
    # Erica's Specific Methodology
    erica_trade_setup: Optional[EricaTradeSetup]
    erica_reasoning: str
    erica_specific_rules: Dict[str, Any]
    
    # Alternative Strategies
    alternative_strategies: List[Tuple[StrategyType, float, str]]  # strategy, score, reason
    
    # Execution Details
    entry_criteria: List[str]
    exit_criteria: List[str]
    position_sizing: Dict[str, Any]
    timing_considerations: List[str]
    
    # Market Context
    market_environment: str
    sector_analysis: str
    relative_strength: str

class EnhancedStrategyAnalyzer:
    """Enhanced analyzer providing detailed strategy-based stock classification"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.market_analyzer = MarketAnalysisEngine(api_key)
        self.strategy_engine = IntelligentStrategyEngine(api_key)
        self.erica_engine = EricaStrategyEngine()
        self.ai_analyzer = AIAnalysisEngine(api_key)
        self.decision_tree = StrategyDecisionTree()
        
        self.logger = logging.getLogger(__name__)
        
        # Load Erica's strategy criteria from slides
        self.strategy_criteria = self._load_erica_strategy_criteria()
        
    def _load_erica_strategy_criteria(self) -> Dict[str, Dict[str, Any]]:
        """Load Erica's specific strategy criteria based on her documented slides"""
        return {
            "bullish_spread": {
                "setup_requirements": [
                    "Buy call you hope the stock can reach by expiration",
                    "Sell a call against it (lower call strike should be below the first call you bought)",
                    "Set Stock Alarm for $1 less than the bottom leg of your spread for a stock that is $75-500 per share"
                ],
                "stock_alarm_criteria": {
                    "price_range": (75, 500),
                    "alarm_offset": -1  # $1 less than bottom leg
                },
                "profit_scenarios": [
                    "No Stock Alarm Notification: Stock turned into a Bull Call Spread because the stock is above the strike price",
                    "Price higher At Expiration: Allow the shares to be called away if the stock price is higher than the covered call's strike price",
                    "Price is Lower at Expiration: Allow covered call to expire, Sell a new covered call on Monday"
                ],
                "management_rules": [
                    "Wait to construct to expire or close the contract for a profit",
                    "Buy 100 shares of stock to cover the bottom leg and turn it into a covered call",
                    "Sell the upside leg for a profit. You can wait for the stock to rise more to get more profit"
                ]
            },
            "covered_call": {
                "basic_requirements": [
                    "Own 100 shares of stock",
                    "Sell call options against the shares",
                    "Target 30-45 DTE (Days to Expiration)",
                    "Target 0.30 delta",
                    "Minimum $30 premium per contract"
                ],
                "erica_specific_rules": {
                    "profit_target": 0.50,  # 50% of max profit
                    "management_dte": 21,   # Manage at 21 DTE if not profitable
                    "iv_rank_minimum": 0.5  # 50th percentile IV minimum
                },
                "earnings_adjustments": {
                    "near_earnings": {
                        "delta_max": 0.10,
                        "use_expected_move": True,
                        "buffer_percentage": 0.10
                    }
                }
            },
            "put_credit_spread": {
                "setup_requirements": [
                    "Bullish or neutral market bias required",
                    "Sell put at 0.15-0.20 delta",
                    "Buy protection 5-10 strikes lower",
                    "30-45 DTE preferred",
                    "Close at 50% profit"
                ],
                "market_conditions": [
                    "Above key support levels",
                    "Bullish market regime",
                    "High IV rank (>60th percentile)"
                ]
            }
        }
        
    def analyze_stock_comprehensive(self, symbol: str, 
                                  analysis_depth: AnalysisDepth = AnalysisDepth.COMPREHENSIVE) -> DetailedStrategyAnalysis:
        """
        Perform comprehensive strategy-based analysis of a stock
        
        Args:
            symbol: Stock symbol to analyze
            analysis_depth: Level of analysis detail
            
        Returns:
            Detailed strategy analysis with comprehensive explanations
        """
        
        self.logger.info(f"Starting comprehensive analysis for {symbol}")
        
        try:
            # Get market and stock factors
            market_factors = self.market_analyzer.analyze_market_factors()
            stock_factors = self.market_analyzer.analyze_stock_factors(symbol)
            
            # Get Erica's strategy recommendation
            erica_signals = self._convert_to_erica_signals(symbol, market_factors, stock_factors)
            market_bias = self._determine_market_bias(market_factors)
            erica_setups = self.erica_engine.analyze_setup(symbol, erica_signals, market_bias)
            
            # Get decision tree recommendation
            strategy_rec = self.decision_tree.recommend_strategy(symbol, market_factors, stock_factors)
            
            # Determine primary strategy (prefer Erica's if available)
            if erica_setups:
                primary_strategy = self._map_erica_to_strategy_type(erica_setups[0].strategy)
                primary_setup = erica_setups[0]
            else:
                primary_strategy = strategy_rec.primary_strategy
                primary_setup = None
            
            # Calculate confidence level
            confidence_level = self._calculate_confidence_level(strategy_rec.confidence)
            
            # Analyze criteria matches
            criteria_matches = self._analyze_criteria_matches(
                primary_strategy, symbol, market_factors, stock_factors, primary_setup
            )
            
            # Generate investment thesis and catalysts
            catalysts = self._identify_key_catalysts(symbol, market_factors, stock_factors, primary_strategy)
            investment_thesis = self._generate_investment_thesis(symbol, primary_strategy, catalysts, criteria_matches)
            
            # Get financial projections
            financial_projections = self._get_financial_projections(symbol)
            
            # Generate supporting metrics
            supporting_metrics = self._generate_supporting_metrics(symbol, market_factors, stock_factors)
            
            # Get alternative strategies
            alternatives = self._get_alternative_strategies(strategy_rec, primary_strategy)
            
            # Generate execution details
            entry_criteria = self._generate_entry_criteria(primary_strategy, symbol, primary_setup)
            exit_criteria = self._generate_exit_criteria(primary_strategy, symbol, primary_setup)
            position_sizing = self._calculate_position_sizing(primary_strategy, symbol, market_factors, stock_factors)
            
            # Create comprehensive analysis
            analysis = DetailedStrategyAnalysis(
                symbol=symbol,
                analysis_timestamp=datetime.now(),
                primary_strategy=primary_strategy,
                strategy_confidence=confidence_level,
                strategy_score=strategy_rec.confidence * 100,
                criteria_matches=criteria_matches,
                criteria_summary=self._generate_criteria_summary(criteria_matches),
                why_this_strategy=self._explain_strategy_selection(primary_strategy, symbol, criteria_matches),
                key_catalysts=catalysts,
                investment_thesis=investment_thesis,
                risk_factors=strategy_rec.key_risk_factors,
                financial_projections=financial_projections,
                analyst_sentiment=self._determine_analyst_sentiment(financial_projections),
                supporting_metrics=supporting_metrics,
                erica_trade_setup=primary_setup,
                erica_reasoning=primary_setup.reasoning if primary_setup else "Standard strategy application",
                erica_specific_rules=self._get_erica_specific_rules(symbol),
                alternative_strategies=alternatives,
                entry_criteria=entry_criteria,
                exit_criteria=exit_criteria,
                position_sizing=position_sizing,
                timing_considerations=self._generate_timing_considerations(primary_strategy, symbol, market_factors),
                market_environment=self._describe_market_environment(market_factors),
                sector_analysis=self._analyze_sector_context(symbol, market_factors),
                relative_strength=self._analyze_relative_strength(symbol, stock_factors)
            )
            
            self.logger.info(f"Completed comprehensive analysis for {symbol}")
            return analysis

        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {str(e)}")
            raise

    def _convert_to_erica_signals(self, symbol: str, market_factors: MarketFactors,
                                 stock_factors: StockSpecificFactors) -> EricaSignals:
        """Convert market/stock factors to Erica's signal format"""
        # Get current price from market data (fallback to $100 for demo)
        spot_price = self._get_current_price(symbol)

        iv_rank_normalized = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100

        return EricaSignals(
            spot_price=spot_price,
            earnings_days_away=stock_factors.earnings_days_away or 30,
            iv_rank=iv_rank_normalized * 100,  # EricaSignals expects 0-100 scale
            iv_percentile=getattr(stock_factors, 'iv_percentile', iv_rank_normalized) * 100,
            expected_move=stock_factors.earnings_move_estimate * spot_price,
            atr=spot_price * 0.02,  # Estimate 2% ATR
            support_level=spot_price * 0.95,  # Estimate support 5% below
            resistance_level=spot_price * 1.05  # Estimate resistance 5% above
        )

    def _get_current_price(self, symbol: str) -> float:
        """Get current stock price (simplified implementation)"""
        # In a real implementation, this would fetch from market data API
        # For demo purposes, use reasonable stock prices
        demo_prices = {
            "AAPL": 175.00,
            "NVDA": 450.00,
            "AMD": 110.00,
            "GOOGL": 140.00,
            "AMZN": 145.00,
            "TSLA": 250.00,
            "MSFT": 380.00
        }
        return demo_prices.get(symbol, 100.00)  # Default to $100

    def _determine_market_bias(self, market_factors: MarketFactors) -> MarketBias:
        """Determine market bias from market factors"""
        if market_factors.market_regime.value == "bull_market":
            return MarketBias.BULLISH
        elif market_factors.market_regime.value == "bear_market":
            return MarketBias.BEARISH
        else:
            return MarketBias.NEUTRAL

    def _map_erica_to_strategy_type(self, erica_strategy) -> StrategyType:
        """Map Erica's strategy enum to system strategy type"""
        strategy_mapping = {
            "BASELINE_CC": StrategyType.COVERED_CALL,
            "FAST_MONEY_CC": StrategyType.COVERED_CALL,
            "EARNINGS_CC": StrategyType.COVERED_CALL,
            "PUT_CREDIT_SPREAD": StrategyType.CREDIT_SPREAD,
            "LEAPS": StrategyType.LEAPS,
            "WHEEL": StrategyType.PREMIUM_SELLING
        }
        return strategy_mapping.get(erica_strategy.value, StrategyType.COVERED_CALL)

    def _calculate_confidence_level(self, confidence_score: float) -> ConfidenceLevel:
        """Convert confidence score to confidence level"""
        if confidence_score >= 0.90:
            return ConfidenceLevel.VERY_HIGH
        elif confidence_score >= 0.75:
            return ConfidenceLevel.HIGH
        elif confidence_score >= 0.60:
            return ConfidenceLevel.MODERATE
        elif confidence_score >= 0.40:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW

    def _analyze_criteria_matches(self, strategy: StrategyType, symbol: str,
                                 market_factors: MarketFactors, stock_factors: StockSpecificFactors,
                                 erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze how well the stock matches strategy criteria"""
        matches = []

        if strategy == StrategyType.COVERED_CALL:
            matches.extend(self._analyze_covered_call_criteria(symbol, market_factors, stock_factors, erica_setup))
        elif strategy == StrategyType.CREDIT_SPREAD:
            matches.extend(self._analyze_credit_spread_criteria(symbol, market_factors, stock_factors, erica_setup))
        elif strategy == StrategyType.LEAPS:
            matches.extend(self._analyze_leaps_criteria(symbol, market_factors, stock_factors, erica_setup))
        elif strategy == StrategyType.PREMIUM_SELLING:
            matches.extend(self._analyze_premium_selling_criteria(symbol, market_factors, stock_factors, erica_setup))

        return matches

    def _analyze_covered_call_criteria(self, symbol: str, market_factors: MarketFactors,
                                     stock_factors: StockSpecificFactors,
                                     erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze covered call specific criteria"""
        matches = []
        criteria = self.strategy_criteria["covered_call"]

        # IV Rank Check
        iv_rank_required = criteria["erica_specific_rules"]["iv_rank_minimum"]
        iv_rank_actual = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
        matches.append(StrategyCriteriaMatch(
            criterion_name="IV Rank Minimum",
            required_value=f">= {iv_rank_required:.0%}",
            actual_value=f"{iv_rank_actual:.0%}",
            is_met=iv_rank_actual >= iv_rank_required,
            score=min(iv_rank_actual / iv_rank_required, 1.0) if iv_rank_required > 0 else 1.0,
            importance="critical",
            explanation=f"Erica requires minimum {iv_rank_required:.0%} IV rank for premium collection. Current IV rank is {iv_rank_actual:.0%}."
        ))

        # Stock-specific rules if available
        if symbol in STOCK_RULES:
            stock_rules = STOCK_RULES[symbol]

            # Delta range check
            target_delta = 0.30  # Default from Erica's methodology
            matches.append(StrategyCriteriaMatch(
                criterion_name="Target Delta Range",
                required_value=f"{stock_rules.cc_delta_range[0]:.2f} - {stock_rules.cc_delta_range[1]:.2f}",
                actual_value=f"Target: {target_delta:.2f}",
                is_met=stock_rules.cc_delta_range[0] <= target_delta <= stock_rules.cc_delta_range[1],
                score=1.0 if stock_rules.cc_delta_range[0] <= target_delta <= stock_rules.cc_delta_range[1] else 0.7,
                importance="important",
                explanation=f"{symbol}-specific delta range is {stock_rules.cc_delta_range[0]:.2f}-{stock_rules.cc_delta_range[1]:.2f}"
            ))

            # DTE range check
            matches.append(StrategyCriteriaMatch(
                criterion_name="Days to Expiration",
                required_value=f"{stock_rules.cc_preferred_dte[0]}-{stock_rules.cc_preferred_dte[1]} days",
                actual_value="Target range met",
                is_met=True,
                score=1.0,
                importance="important",
                explanation=f"{symbol} preferred DTE range: {stock_rules.cc_preferred_dte[0]}-{stock_rules.cc_preferred_dte[1]} days"
            ))

        # Premium requirement
        min_premium = 0.30  # $30 minimum per Erica's rules
        current_price = self._get_current_price(symbol)
        estimated_premium = current_price * 0.02  # Rough estimate
        matches.append(StrategyCriteriaMatch(
            criterion_name="Minimum Premium",
            required_value=f">= ${min_premium:.2f}",
            actual_value=f"~${estimated_premium:.2f} (estimated)",
            is_met=estimated_premium >= min_premium,
            score=min(estimated_premium / min_premium, 1.0) if min_premium > 0 else 1.0,
            importance="important",
            explanation=f"Erica requires minimum ${min_premium:.2f} premium per contract for income generation"
        ))

        return matches

    def _analyze_credit_spread_criteria(self, symbol: str, market_factors: MarketFactors,
                                      stock_factors: StockSpecificFactors,
                                      erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze put credit spread specific criteria"""
        matches = []
        criteria = self.strategy_criteria["put_credit_spread"]

        # Market bias requirement
        market_bias = self._determine_market_bias(market_factors)
        is_bullish_neutral = market_bias in [MarketBias.BULLISH, MarketBias.NEUTRAL]
        matches.append(StrategyCriteriaMatch(
            criterion_name="Market Bias",
            required_value="Bullish or Neutral",
            actual_value=market_bias.value.title(),
            is_met=is_bullish_neutral,
            score=1.0 if is_bullish_neutral else 0.3,
            importance="critical",
            explanation="Put credit spreads require bullish or neutral market conditions for optimal performance"
        ))

        # IV Rank for credit spreads
        iv_rank_required = 0.60  # 60th percentile for credit spreads
        iv_rank_actual = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
        matches.append(StrategyCriteriaMatch(
            criterion_name="IV Rank for Credit Collection",
            required_value=f">= {iv_rank_required:.0%}",
            actual_value=f"{iv_rank_actual:.0%}",
            is_met=iv_rank_actual >= iv_rank_required,
            score=min(iv_rank_actual / iv_rank_required, 1.0) if iv_rank_required > 0 else 1.0,
            importance="critical",
            explanation=f"Credit spreads need elevated IV (>={iv_rank_required:.0%}) for attractive premium collection"
        ))

        # Support level analysis
        current_price = self._get_current_price(symbol)
        support_level = current_price * 0.95  # Estimate support 5% below current price
        distance_from_support = (current_price - support_level) / current_price
        is_above_support = distance_from_support > 0.05  # 5% buffer

        matches.append(StrategyCriteriaMatch(
            criterion_name="Distance from Support",
            required_value="> 5% above support",
            actual_value=f"{distance_from_support:.1%} above support",
            is_met=is_above_support,
            score=min(distance_from_support / 0.05, 1.0) if distance_from_support > 0 else 0.0,
            importance="important",
            explanation=f"Stock should be well above support levels. Currently {distance_from_support:.1%} above support at ${support_level:.2f}"
        ))

        return matches

    def _analyze_leaps_criteria(self, symbol: str, market_factors: MarketFactors,
                               stock_factors: StockSpecificFactors,
                               erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze LEAPS specific criteria"""
        matches = []

        # Long-term trend requirement
        trend_strength = getattr(stock_factors, 'trend_strength', 0.6)  # Assume moderate trend
        matches.append(StrategyCriteriaMatch(
            criterion_name="Strong Long-term Trend",
            required_value="Strong uptrend required",
            actual_value=f"Trend strength: {trend_strength:.1%}",
            is_met=trend_strength > 0.7,
            score=trend_strength,
            importance="critical",
            explanation="LEAPS require strong long-term uptrend for leveraged growth exposure"
        ))

        # IV Rank (prefer lower for buying options)
        iv_rank_actual = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
        iv_rank_preferred_max = 0.60  # Prefer lower IV when buying
        matches.append(StrategyCriteriaMatch(
            criterion_name="IV Rank (Lower Preferred)",
            required_value=f"< {iv_rank_preferred_max:.0%} preferred",
            actual_value=f"{iv_rank_actual:.0%}",
            is_met=iv_rank_actual < iv_rank_preferred_max,
            score=max(0.2, 1.0 - (iv_rank_actual / iv_rank_preferred_max)),
            importance="important",
            explanation="Lower IV preferred when buying LEAPS to reduce premium cost"
        ))

        return matches

    def _analyze_premium_selling_criteria(self, symbol: str, market_factors: MarketFactors,
                                        stock_factors: StockSpecificFactors,
                                        erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze premium selling (wheel) criteria"""
        matches = []

        # High IV requirement for premium selling
        iv_rank_required = 0.70  # 70th percentile for systematic premium selling
        iv_rank_actual = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
        matches.append(StrategyCriteriaMatch(
            criterion_name="High IV for Premium Collection",
            required_value=f">= {iv_rank_required:.0%}",
            actual_value=f"{iv_rank_actual:.0%}",
            is_met=iv_rank_actual >= iv_rank_required,
            score=min(iv_rank_actual / iv_rank_required, 1.0) if iv_rank_required > 0 else 1.0,
            importance="critical",
            explanation="Systematic premium selling requires consistently high IV for attractive returns"
        ))

        # Stock quality for potential assignment
        matches.append(StrategyCriteriaMatch(
            criterion_name="Stock Quality for Assignment",
            required_value="High-quality stock acceptable for ownership",
            actual_value="Quality stock suitable for wheel strategy",
            is_met=True,  # Assume quality stocks in our universe
            score=0.9,
            importance="important",
            explanation="Must be comfortable owning the stock if assigned on puts"
        ))

        return matches

    def _identify_key_catalysts(self, symbol: str, market_factors: MarketFactors,
                               stock_factors: StockSpecificFactors,
                               strategy: StrategyType) -> List[CatalystAnalysis]:
        """Identify key catalysts driving the investment thesis"""
        catalysts = []

        # Earnings catalyst
        earnings_days = getattr(stock_factors, 'earnings_days_away', 30)
        if earnings_days <= 10:
            catalysts.append(CatalystAnalysis(
                catalyst_type="earnings",
                description=f"Earnings announcement in {earnings_days} days",
                impact_level="high",
                timeframe="immediate",
                probability=1.0,
                supporting_evidence=[
                    f"Earnings expected in {earnings_days} trading days",
                    f"Current IV rank: {stock_factors.iv_rank:.0%}",
                    "Elevated volatility expected around earnings"
                ]
            ))

        # Technical catalyst
        if hasattr(stock_factors, 'near_resistance') and stock_factors.near_resistance:
            catalysts.append(CatalystAnalysis(
                catalyst_type="technical",
                description="Approaching key resistance level",
                impact_level="medium",
                timeframe="short_term",
                probability=0.7,
                supporting_evidence=[
                    f"Current price: ${stock_factors.current_price:.2f}",
                    f"Resistance level: ${getattr(stock_factors, 'resistance_level', stock_factors.current_price * 1.05):.2f}",
                    "Technical breakout potential"
                ]
            ))

        # Market regime catalyst
        if market_factors.market_regime.value == "bull_market":
            catalysts.append(CatalystAnalysis(
                catalyst_type="market",
                description="Favorable bull market environment",
                impact_level="medium",
                timeframe="medium_term",
                probability=0.8,
                supporting_evidence=[
                    "Bull market regime detected",
                    "Positive market momentum",
                    "Risk-on sentiment supporting growth stocks"
                ]
            ))

        return catalysts

    def _generate_investment_thesis(self, symbol: str, strategy: StrategyType,
                                   catalysts: List[CatalystAnalysis],
                                   criteria_matches: List[StrategyCriteriaMatch]) -> str:
        """Generate comprehensive investment thesis"""

        # Count criteria that are met
        met_criteria = sum(1 for match in criteria_matches if match.is_met)
        total_criteria = len(criteria_matches)

        # Strategy-specific thesis
        strategy_thesis = {
            StrategyType.COVERED_CALL: f"{symbol} is well-suited for covered call income generation due to elevated implied volatility and stable price action. The strategy allows for premium collection while maintaining upside participation.",
            StrategyType.CREDIT_SPREAD: f"{symbol} presents an attractive put credit spread opportunity with the stock trading above key support levels in a favorable market environment. The elevated IV provides attractive premium collection potential.",
            StrategyType.LEAPS: f"{symbol} shows strong long-term growth potential making it suitable for LEAPS strategy. The leveraged exposure allows for participation in the stock's growth trajectory with defined risk.",
            StrategyType.PREMIUM_SELLING: f"{symbol} is ideal for systematic premium selling (wheel strategy) due to consistently high implied volatility and stock quality suitable for potential ownership."
        }

        base_thesis = strategy_thesis.get(strategy, f"{symbol} fits the selected strategy based on current market conditions.")

        # Add catalyst support
        catalyst_support = ""
        if catalysts:
            high_impact_catalysts = [c for c in catalysts if c.impact_level == "high"]
            if high_impact_catalysts:
                catalyst_support = f" Key catalysts include {', '.join([c.description.lower() for c in high_impact_catalysts])}."

        # Add criteria confidence
        criteria_confidence = f" The analysis shows {met_criteria}/{total_criteria} key criteria are met, providing {'strong' if met_criteria/total_criteria > 0.8 else 'moderate' if met_criteria/total_criteria > 0.6 else 'limited'} confidence in the strategy selection."

        return base_thesis + catalyst_support + criteria_confidence

    def _get_financial_projections(self, symbol: str) -> FinancialProjections:
        """Get financial analyst projections (mock implementation)"""
        # In a real implementation, this would fetch from financial data APIs
        return FinancialProjections(
            price_target_mean=None,
            price_target_high=None,
            price_target_low=None,
            analyst_rating="Hold",
            eps_estimate_current=None,
            eps_estimate_next=None,
            revenue_growth_estimate=None,
            analyst_count=0,
            upgrade_downgrade_trend="Neutral",
            key_analyst_notes=["Financial projections would be fetched from data provider"]
        )

    def _generate_supporting_metrics(self, symbol: str, market_factors: MarketFactors,
                                   stock_factors: StockSpecificFactors) -> SupportingMetrics:
        """Generate comprehensive supporting metrics"""
        current_price = self._get_current_price(symbol)
        iv_rank_normalized = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100

        return SupportingMetrics(
            technical_indicators={
                "current_price": f"${current_price:.2f}",
                "iv_rank": f"{iv_rank_normalized:.0%}",
                "trend_alignment": stock_factors.trend_alignment,
                "technical_confluence": f"{stock_factors.technical_confluence_score:.1%}",
                "relative_strength": f"{stock_factors.relative_strength_vs_spy:.1%}"
            },
            fundamental_metrics={
                "earnings_days_away": stock_factors.earnings_days_away or "N/A",
                "earnings_move_estimate": f"{stock_factors.earnings_move_estimate:.1%}",
                "news_sentiment": f"{stock_factors.news_sentiment_score:.2f}",
                "sector_performance": f"{stock_factors.sector_performance:.1%}"
            },
            options_metrics={
                "iv_rank": f"{iv_rank_normalized:.0%}",
                "iv_vs_hv_ratio": f"{stock_factors.iv_vs_hv_ratio:.2f}",
                "options_flow_sentiment": stock_factors.options_flow_sentiment,
                "unusual_activity": stock_factors.unusual_options_activity
            },
            market_metrics={
                "market_regime": market_factors.market_regime.value,
                "volatility_regime": market_factors.volatility_regime.value,
                "vix_level": f"{market_factors.vix_level:.1f}",
                "market_breadth": f"{market_factors.market_breadth:.2f}"
            },
            risk_metrics={
                "support_resistance_clarity": f"{stock_factors.support_resistance_clarity:.1%}",
                "earnings_proximity": "High" if stock_factors.earnings_days_away and stock_factors.earnings_days_away < 10 else "Low",
                "sector_rotation_impact": stock_factors.sector_rotation_impact
            },
            comparative_analysis={
                "sector_performance": f"Sector: {stock_factors.sector_performance:.1%}",
                "relative_strength_vs_spy": f"vs SPY: {stock_factors.relative_strength_vs_spy:.1%}",
                "technical_confluence": f"Technical: {stock_factors.technical_confluence_score:.1%}"
            }
        )

    def _get_alternative_strategies(self, strategy_rec: StrategyRecommendation,
                                   primary_strategy: StrategyType) -> List[Tuple[StrategyType, float, str]]:
        """Get alternative strategy recommendations"""
        alternatives = []
        for alt_strategy, score in strategy_rec.alternative_strategies:
            if alt_strategy != primary_strategy:
                reason = f"Alternative with {score:.1%} confidence based on current market conditions"
                alternatives.append((alt_strategy, score, reason))
        return alternatives[:3]  # Top 3 alternatives

    def _generate_entry_criteria(self, strategy: StrategyType, symbol: str,
                               erica_setup: Optional[EricaTradeSetup]) -> List[str]:
        """Generate specific entry criteria"""
        if erica_setup:
            return [
                f"Target delta: {erica_setup.target_delta:.2f}",
                f"Days to expiration: {erica_setup.dte}",
                f"Strike price: ${erica_setup.short_strike:.2f}" if erica_setup.short_strike else "Strike TBD",
                "Confirm IV rank above minimum threshold",
                "Verify market conditions remain favorable"
            ]

        # Default criteria by strategy
        criteria_map = {
            StrategyType.COVERED_CALL: [
                "Own 100 shares of stock",
                "IV rank > 50th percentile",
                "Target 0.30 delta call option",
                "30-45 DTE preferred",
                "Minimum $30 premium target"
            ],
            StrategyType.CREDIT_SPREAD: [
                "Bullish/neutral market bias confirmed",
                "Stock above key support levels",
                "IV rank > 60th percentile",
                "Target 0.15-0.20 delta short put",
                "30-45 DTE preferred"
            ],
            StrategyType.LEAPS: [
                "Strong long-term uptrend confirmed",
                "IV rank < 60th percentile preferred",
                "Target 0.70-0.80 delta",
                "12+ months to expiration",
                "Fundamental growth story intact"
            ],
            StrategyType.PREMIUM_SELLING: [
                "IV rank > 70th percentile",
                "Comfortable owning the stock",
                "Systematic approach to strike selection",
                "Consistent premium collection opportunity"
            ]
        }

        return criteria_map.get(strategy, ["Standard entry criteria apply"])

    def _generate_exit_criteria(self, strategy: StrategyType, symbol: str,
                              erica_setup: Optional[EricaTradeSetup]) -> List[str]:
        """Generate specific exit criteria"""
        if erica_setup:
            return [
                f"Profit target: {erica_setup.profit_target_pct:.0%} of max profit",
                f"Time exit: {erica_setup.time_exit_dte} DTE if unclear",
                f"Roll trigger: {erica_setup.roll_trigger_delta:.2f} delta breach",
                "Monitor for early assignment risk",
                "Adjust based on changing market conditions"
            ]

        # Default exit criteria by strategy
        criteria_map = {
            StrategyType.COVERED_CALL: [
                "Close at 50% of max profit",
                "Roll at 21 DTE if unprofitable",
                "Manage if delta exceeds 0.35",
                "Consider early assignment risk"
            ],
            StrategyType.CREDIT_SPREAD: [
                "Close at 50% of max profit",
                "Exit by 5 DTE if unclear",
                "Roll if breaching support",
                "Monitor for adverse market changes"
            ],
            StrategyType.LEAPS: [
                "Hold for 6-12 months typically",
                "Exit if fundamental thesis changes",
                "Take profits on significant moves",
                "Monitor time decay acceleration"
            ],
            StrategyType.PREMIUM_SELLING: [
                "Systematic profit taking at 50-70%",
                "Roll challenged positions",
                "Accept assignment if favorable",
                "Maintain consistent approach"
            ]
        }

        return criteria_map.get(strategy, ["Standard exit criteria apply"])

    def _calculate_position_sizing(self, strategy: StrategyType, symbol: str,
                                 market_factors: MarketFactors,
                                 stock_factors: StockSpecificFactors) -> Dict[str, Any]:
        """Calculate appropriate position sizing"""
        base_size = 1.0  # Base position multiplier

        # Adjust for volatility
        volatility = getattr(stock_factors, 'volatility', 0.25)
        if volatility > 0.35:
            volatility_adjustment = 0.7  # Reduce size for high volatility
        elif volatility < 0.15:
            volatility_adjustment = 1.2  # Increase size for low volatility
        else:
            volatility_adjustment = 1.0

        # Adjust for market conditions
        market_adjustment = 1.0
        if market_factors.market_regime.value == "bear_market":
            market_adjustment = 0.8
        elif market_factors.market_regime.value == "bull_market":
            market_adjustment = 1.1

        final_multiplier = base_size * volatility_adjustment * market_adjustment

        return {
            "base_multiplier": base_size,
            "volatility_adjustment": volatility_adjustment,
            "market_adjustment": market_adjustment,
            "final_multiplier": final_multiplier,
            "recommended_contracts": max(1, int(final_multiplier)),
            "risk_considerations": [
                f"Volatility: {volatility:.1%}",
                f"Market regime: {market_factors.market_regime.value}",
                "Position size adjusted for current conditions"
            ]
        }

    def _generate_timing_considerations(self, strategy: StrategyType, symbol: str,
                                      market_factors: MarketFactors) -> List[str]:
        """Generate timing considerations for strategy execution"""
        considerations = []

        # Market timing
        if market_factors.market_regime.value == "bull_market":
            considerations.append("Favorable bull market environment supports strategy execution")
        elif market_factors.market_regime.value == "bear_market":
            considerations.append("Bear market conditions require defensive positioning")

        # Volatility timing
        if market_factors.volatility_regime.value == "high_vol":
            considerations.append("High volatility environment - favorable for premium selling")
        elif market_factors.volatility_regime.value == "low_vol":
            considerations.append("Low volatility environment - consider premium buying strategies")

        # Strategy-specific timing
        strategy_timing = {
            StrategyType.COVERED_CALL: [
                "Best executed when IV rank > 50th percentile",
                "Avoid right before earnings unless specifically targeting IV crush"
            ],
            StrategyType.CREDIT_SPREAD: [
                "Execute when stock is above support levels",
                "Time entry with bullish market momentum"
            ],
            StrategyType.LEAPS: [
                "Best purchased during low IV periods",
                "Time entry with strong fundamental catalysts"
            ],
            StrategyType.PREMIUM_SELLING: [
                "Systematic execution regardless of market timing",
                "Increase frequency during high IV periods"
            ]
        }

        considerations.extend(strategy_timing.get(strategy, []))
        return considerations

    def _describe_market_environment(self, market_factors: MarketFactors) -> str:
        """Describe current market environment"""
        regime = market_factors.market_regime.value.replace('_', ' ').title()
        vol_regime = market_factors.volatility_regime.value.replace('_', ' ').title()

        return f"{regime} market with {vol_regime} volatility regime. " \
               f"Market conditions are {'favorable' if regime == 'Bull Market' else 'challenging' if regime == 'Bear Market' else 'neutral'} " \
               f"for most options strategies."

    def _analyze_sector_context(self, symbol: str, market_factors: MarketFactors) -> str:
        """Analyze sector context (simplified implementation)"""
        # In a real implementation, this would analyze sector-specific factors
        return f"{symbol} operates in a sector that is currently showing " \
               f"{'strong' if market_factors.market_regime.value == 'bull_market' else 'mixed'} performance " \
               f"relative to the broader market."

    def _analyze_relative_strength(self, symbol: str, stock_factors: StockSpecificFactors) -> str:
        """Analyze relative strength"""
        # Simplified relative strength analysis
        beta = getattr(stock_factors, 'beta', 1.0)
        if beta > 1.2:
            return f"{symbol} shows high beta ({beta:.2f}), indicating higher volatility than market"
        elif beta < 0.8:
            return f"{symbol} shows low beta ({beta:.2f}), indicating lower volatility than market"
        else:
            return f"{symbol} shows moderate beta ({beta:.2f}), moving roughly in line with market"

    def _get_erica_specific_rules(self, symbol: str) -> Dict[str, Any]:
        """Get Erica's specific rules for the symbol"""
        if symbol in STOCK_RULES:
            rules = STOCK_RULES[symbol]
            return {
                "cc_preferred_dte": rules.cc_preferred_dte,
                "cc_delta_range": rules.cc_delta_range,
                "cc_earnings_adjustment": rules.cc_earnings_adjustment,
                "cc_frequency": rules.cc_frequency,
                "pcs_width_points": rules.pcs_width_points,
                "pcs_support_buffer": rules.pcs_support_buffer,
                "leaps_preferred_timeframe": rules.leaps_preferred_timeframe,
                "wheel_frequency": rules.wheel_frequency
            }
        return {"note": f"No specific rules defined for {symbol}, using standard Erica methodology"}

    def _generate_criteria_summary(self, criteria_matches: List[StrategyCriteriaMatch]) -> str:
        """Generate summary of criteria matching"""
        total = len(criteria_matches)
        met = sum(1 for match in criteria_matches if match.is_met)
        critical_met = sum(1 for match in criteria_matches if match.is_met and match.importance == "critical")
        critical_total = sum(1 for match in criteria_matches if match.importance == "critical")

        if critical_total > 0 and critical_met == critical_total:
            confidence = "High"
        elif met / total > 0.8:
            confidence = "Strong"
        elif met / total > 0.6:
            confidence = "Moderate"
        else:
            confidence = "Limited"

        return f"{confidence} criteria match: {met}/{total} criteria met " \
               f"({critical_met}/{critical_total} critical criteria met)"

    def _explain_strategy_selection(self, strategy: StrategyType, symbol: str,
                                   criteria_matches: List[StrategyCriteriaMatch]) -> str:
        """Explain why this strategy was selected"""
        met_criteria = [match for match in criteria_matches if match.is_met]
        key_reasons = [match.explanation for match in met_criteria if match.importance in ["critical", "important"]]

        base_explanation = f"{symbol} is classified as {strategy.value.replace('_', ' ').title()} because "

        if key_reasons:
            reasons_text = ". ".join(key_reasons[:3])  # Top 3 reasons
            return base_explanation + reasons_text.lower() + "."
        else:
            return base_explanation + "it meets the basic requirements for this strategy type."

    def _determine_analyst_sentiment(self, projections: FinancialProjections) -> str:
        """Determine overall analyst sentiment"""
        if projections.analyst_rating:
            rating = projections.analyst_rating.lower()
            if "strong buy" in rating or "buy" in rating:
                return "Bullish"
            elif "sell" in rating:
                return "Bearish"
            else:
                return "Neutral"
        return "No analyst coverage available"

def create_enhanced_analyzer(api_key: str) -> EnhancedStrategyAnalyzer:
    """Factory function to create enhanced strategy analyzer"""
    return EnhancedStrategyAnalyzer(api_key)
