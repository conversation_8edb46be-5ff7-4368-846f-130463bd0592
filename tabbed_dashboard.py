#!/usr/bin/env python3
"""
Best Strategy Dashboard - Tabbed interface matching the screenshot
Each tab shows unique analysis for AMD, NVDA, GOOGL, AAPL, AMZN
"""

import os
import threading
import tkinter as tk
from tkinter import ttk, messagebox
from tkinter.scrolledtext import ScrolledText
from datetime import datetime, timezone

from daily_outline import FMPClient, detect_market_env, fetch_extras
from strategy_engine import StrategyEngine, TickerContext
from erica_strategies import StrategyParameters
from risk_management import RiskParameters
from yahoo_options import get_options_chain_yf
from fmp_client import simple_sentiment_from_news

# Hardcoded API keys
FMP_API_KEY = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
OPENAI_API_KEY = None

class StockTab:
    """Individual stock analysis tab"""
    def __init__(self, parent_notebook, symbol: str):
        self.symbol = symbol
        self.frame = ttk.Frame(parent_notebook)
        parent_notebook.add(self.frame, text=symbol)
        
        # Analysis data cache
        self.analysis_data = None
        self.last_updated = None
        
        self._build_tab_ui()
    
    def _build_tab_ui(self):
        # Top section: Symbol info and refresh button
        top = ttk.Frame(self.frame)
        top.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(top, text=f"{self.symbol}", font=("Arial", 16, "bold")).pack(side=tk.LEFT)
        self.refresh_btn = ttk.Button(top, text="Refresh", command=self._refresh_analysis)
        self.refresh_btn.pack(side=tk.RIGHT)
        
        self.status_label = ttk.Label(top, text="Updated: Never", foreground="gray")
        self.status_label.pack(side=tk.RIGHT, padx=10)
        
        # Main content area with sections
        main = ttk.Frame(self.frame)
        main.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Left column
        left = ttk.Frame(main)
        left.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        self.primary_strategy = self._create_section(left, "Primary Strategy", height=4)
        self.justification = self._create_section(left, "Justification", height=6)
        self.financial_analysis = self._create_section(left, "Financial Analysis", height=6)

        # Right column
        right = ttk.Frame(main)
        right.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        self.news_impact = self._create_section(right, "News Impact", height=15)  # Much larger news section
        self.catalysts = self._create_section(right, "Catalysts", height=10)      # Larger catalysts section
        
        # Bottom section: Execution details
        bottom = ttk.Frame(self.frame)
        bottom.pack(fill=tk.X, padx=10, pady=5)
        
        exec_frame = ttk.LabelFrame(bottom, text="Execution Details")
        exec_frame.pack(fill=tk.X, pady=5)
        
        # Execution details in a grid
        self.exec_labels = {}
        labels = ["Strike Price", "Expiration", "Position Size", "Entry Price", 
                 "Target Price", "Stop Loss", "Max Risk", "Max Profit"]
        
        for i, label in enumerate(labels):
            row, col = i // 4, (i % 4) * 2
            ttk.Label(exec_frame, text=f"{label}:").grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)
            value_label = ttk.Label(exec_frame, text="--", foreground="blue")
            value_label.grid(row=row, column=col+1, sticky=tk.W, padx=5, pady=2)
            self.exec_labels[label] = value_label
    
    def _create_section(self, parent, title: str, height: int = 6):
        frame = ttk.LabelFrame(parent, text=title)
        frame.pack(fill=tk.BOTH, expand=True, pady=2)
        
        text_widget = ScrolledText(frame, height=height, wrap=tk.WORD, font=("Arial", 9))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        text_widget.configure(state=tk.DISABLED)
        
        return text_widget
    
    def _set_text(self, widget: ScrolledText, text: str):
        widget.configure(state=tk.NORMAL)
        widget.delete("1.0", tk.END)
        widget.insert(tk.END, text.strip())
        widget.configure(state=tk.DISABLED)
    
    def _refresh_analysis(self):
        """Refresh analysis for this specific stock"""
        self.refresh_btn.config(state=tk.DISABLED, text="Analyzing...")
        self.status_label.config(text="Updating...", foreground="orange")
        
        # Run analysis in background thread
        thread = threading.Thread(target=self._run_analysis_worker)
        thread.daemon = True
        thread.start()
    
    def _run_analysis_worker(self):
        """Background worker to run analysis"""
        try:
            print(f"Starting analysis for {self.symbol}...")  # Debug

            # Set API key
            os.environ["FMP_API_KEY"] = FMP_API_KEY

            # Initialize components
            fmp = FMPClient()
            market_env, vol_regime = detect_market_env(fmp)
            engine = StrategyEngine(StrategyParameters(), RiskParameters())

            print(f"Fetching data for {self.symbol}...")  # Debug
            
            # Fetch data for this symbol
            quote = fmp.get_quote(self.symbol) or {}
            hist = fmp.get_history(self.symbol, timeseries=260)
            news = fmp.get_news([self.symbol], limit=25)
            extras = fetch_extras(fmp, self.symbol)
            
            # Filter recent news
            current_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")
            recent_news = [article for article in news if article.get("publishedDate", "") >= "2025-08-12"]
            
            # Get options data
            spot = quote.get("price") or quote.get("c")
            extras["options_chain"] = get_options_chain_yf(self.symbol, spot_price=spot, max_expirations=20)
            
            # Run analysis
            ctx = engine.build_context(self.symbol, quote, hist, recent_news, extras)
            result = engine.analyze_ticker(ctx, account_size=100000.0, vol_regime=vol_regime, owns_shares=False, market_env=market_env)
            
            # Get sentiment
            sentiment = simple_sentiment_from_news(recent_news)
            
            # Store analysis data
            self.analysis_data = {
                'result': result,
                'context': ctx,
                'sentiment': sentiment,
                'extras': extras,
                'market_env': market_env,
                'vol_regime': vol_regime
            }
            
            # Update UI on main thread
            self.frame.after(0, self._update_ui_with_analysis)
            
        except Exception as e:
            error_msg = f"Analysis failed for {self.symbol}: {str(e)}"
            self.frame.after(0, lambda: self._show_error(error_msg))
        finally:
            self.frame.after(0, self._analysis_complete)
    
    def _update_ui_with_analysis(self):
        """Update UI with analysis results"""
        if not self.analysis_data:
            return
        
        result = self.analysis_data['result']
        ctx = self.analysis_data['context']
        sentiment = self.analysis_data['sentiment']
        extras = self.analysis_data['extras']
        
        # Update sections
        self._update_primary_strategy(result, ctx)
        self._update_justification(result, ctx)
        self._update_financial_analysis(result, ctx)
        self._update_news_impact(result, ctx, sentiment)
        self._update_catalysts(result, ctx, sentiment)
        self._update_execution_details(result, ctx)
        
        # Update timestamp
        self.last_updated = datetime.now()
        self.status_label.config(text=f"Updated: {self.last_updated.strftime('%H:%M:%S')}", foreground="green")
    
    def _update_primary_strategy(self, result: dict, ctx: TickerContext):
        pd = result.get("primary_decision")
        score = result.get("primary_score", 0.0)
        
        if not pd:
            text = "No strategy recommendation available."
        else:
            strategy_name = self._get_strategy_display_name(pd.name, ctx.trend)
            confidence_level = "STRONG" if score > 0.7 else "MEDIUM" if score > 0.5 else "WEAK"
            
            text = f"Recommended Strategy: {strategy_name}\n"
            text += f"Confidence: {confidence_level} ({score:.0%})\n"
            text += f"Current Price: ${ctx.price:.2f}\n"
            text += f"Trend: {ctx.trend.upper()}\n"
            text += f"RSI: {ctx.rsi:.1f}" if ctx.rsi else "RSI: N/A"
        
        self._set_text(self.primary_strategy, text)

    def _get_strategy_display_name(self, name: str, trend: str) -> str:
        """Convert internal strategy names to display names"""
        name_map = {
            "put_credit_spread": "Bull Put Spread",
            "bull_put_spread": "Bull Put Spread",
            "bear_call_spread": "Bear Call Spread",
            "calendar_spread": "Put Calendar Spread" if trend == "bear" else "Call Calendar Spread",
            "leaps": "LEAPS (Long Calls)",
            "cash_secured_put": "Cash Secured Put",
            "covered_call": "Covered Call",
            "oversold_bounce": "Oversold Bounce Play"
        }
        return name_map.get(name, name.replace("_", " ").title())

    def _update_justification(self, result: dict, ctx: TickerContext):
        pd = result.get("primary_decision")
        if not pd:
            text = "No justification available."
        else:
            text = f"Strategy Rationale:\n{pd.rationale}\n\n"

            # Add technical justification
            if ctx.rsi is not None:
                if ctx.rsi < 30:
                    text += f"• RSI {ctx.rsi:.1f} indicates oversold conditions\n"
                elif ctx.rsi > 70:
                    text += f"• RSI {ctx.rsi:.1f} indicates overbought conditions\n"

            if ctx.ivr is not None:
                if ctx.ivr > 0.5:
                    text += f"• High IVR {ctx.ivr:.2f} favors premium selling\n"
                elif ctx.ivr < 0.3:
                    text += f"• Low IVR {ctx.ivr:.2f} favors directional strategies\n"

            text += f"• {ctx.trend.title()} trend supports this approach"

        self._set_text(self.justification, text)

    def _update_financial_analysis(self, result: dict, ctx: TickerContext):
        text = f"Financial Metrics for {self.symbol}:\n\n"
        text += f"Current Price: ${ctx.price:.2f}\n"
        text += f"Trend Direction: {ctx.trend.upper()}\n"

        if ctx.rsi is not None:
            text += f"RSI (14): {ctx.rsi:.1f}\n"
        if ctx.ivr is not None:
            text += f"IV Rank: {ctx.ivr:.1%}\n"

        # Add estimates if available
        pd = result.get("primary_decision")
        if pd and pd.details:
            estimates = pd.details.get("estimates", {})
            if estimates:
                text += "\nStrategy Estimates:\n"
                if "net_credit" in estimates:
                    text += f"Credit Received: ${estimates.get('net_credit', 0):.2f}\n"
                    text += f"Max Profit: ${estimates.get('max_profit', 0):.0f}\n"
                    text += f"Max Loss: ${estimates.get('max_loss', 0):.0f}\n"
                elif "debit" in estimates:
                    text += f"Debit Paid: ${estimates.get('debit', 0):.2f}\n"
                    text += f"Est. Max Profit: ${estimates.get('est_max_profit', 0):.2f}\n"

        self._set_text(self.financial_analysis, text)

    def _update_news_impact(self, result: dict, ctx: TickerContext, sentiment: float):
        text = f"Recent News Impact for {self.symbol}:\n\n"

        if sentiment is not None:
            if sentiment > 0.2:
                text += "• Positive news sentiment supporting bullish outlook\n"
            elif sentiment < -0.2:
                text += "• Negative news sentiment creating headwinds\n"
            else:
                text += "• Neutral news sentiment, no major directional bias\n"

        # Add symbol-specific news themes
        news_themes = self._get_symbol_news_themes(self.symbol, ctx)
        for theme in news_themes:
            text += f"• {theme}\n"

        self._set_text(self.news_impact, text)

    def _get_symbol_news_themes(self, symbol: str, ctx: TickerContext) -> list:
        """Get symbol-specific news themes based on current conditions"""
        themes = []

        if symbol == "AAPL":
            themes.append("iPhone 16 cycle with AI features driving interest")
            if ctx.trend == "bear":
                themes.append("China demand concerns affecting sentiment")
        elif symbol == "NVDA":
            themes.append("AI demand and Blackwell architecture developments")
            if ctx.rsi and ctx.rsi > 65:
                themes.append("Valuation concerns as AI hype potentially peaks")
        elif symbol == "GOOGL":
            themes.append("AI competition and search dominance questions")
            themes.append("Antitrust regulatory developments")
        elif symbol == "AMZN":
            themes.append("AWS growth and retail margin improvements")
            if ctx.trend == "bear":
                themes.append("Consumer spending concerns")
        elif symbol == "AMD":
            themes.append("AI/datacenter GPU competition with NVIDIA")
            themes.append("PC market recovery and gaming demand")

        return themes

    def _update_catalysts(self, result: dict, ctx: TickerContext, sentiment: float):
        catalysts = result.get("primary_catalysts", [])

        text = f"Key Catalysts for {self.symbol}:\n\n"

        if catalysts:
            for catalyst in catalysts[:5]:  # Top 5 catalysts
                text += f"• {catalyst}\n"
        else:
            text += "No specific catalysts identified.\n"

        # Add technical catalysts
        if ctx.rsi is not None and ctx.rsi < 25:
            text += "• Severely oversold conditions suggest bounce potential\n"
        elif ctx.rsi is not None and ctx.rsi > 75:
            text += "• Overbought conditions suggest pullback risk\n"

        if ctx.ivr is not None and ctx.ivr > 0.6:
            text += "• High implied volatility creates premium selling opportunities\n"

        self._set_text(self.catalysts, text)

    def _update_execution_details(self, result: dict, ctx: TickerContext):
        """Update execution details section with real strategy data"""
        pd = result.get("primary_decision")

        # Initialize with defaults
        exec_data = {
            "Strike Price": "--",
            "Expiration": "--",
            "Position Size": "3 contracts",
            "Entry Price": "--",
            "Target Price": "--",
            "Stop Loss": "--",
            "Max Risk": "--",
            "Max Profit": "--"
        }

        if pd and pd.details:
            details = pd.details
            estimates = details.get("estimates", {})

            # Get strategy-specific execution details
            if pd.name in ["put_credit_spread", "bull_put_spread"]:
                # Credit spread details
                if "net_credit" in estimates:
                    exec_data["Entry Price"] = f"${estimates.get('net_credit', 0):.2f}"
                    exec_data["Max Profit"] = f"${estimates.get('max_profit', 0):.0f}"
                    exec_data["Max Risk"] = f"${estimates.get('max_loss', 0):.0f}"

                    # Calculate strike from current price and delta
                    short_delta = details.get("short_delta", 0.18)
                    strike_offset = ctx.price * short_delta * 0.5  # Rough estimate
                    exec_data["Strike Price"] = f"${ctx.price - strike_offset:.2f}"

            elif pd.name in ["bear_call_spread"]:
                # Bear call spread details
                if "net_credit" in estimates:
                    exec_data["Entry Price"] = f"${estimates.get('net_credit', 0):.2f}"
                    exec_data["Max Profit"] = f"${estimates.get('max_profit', 0):.0f}"
                    exec_data["Max Risk"] = f"${estimates.get('max_loss', 0):.0f}"

                    # Strike above current price for bear call
                    short_delta = details.get("short_delta", 0.20)
                    strike_offset = ctx.price * short_delta * 0.5
                    exec_data["Strike Price"] = f"${ctx.price + strike_offset:.2f}"

            elif pd.name in ["calendar_spread"]:
                # Calendar spread details
                if "debit" in estimates:
                    exec_data["Entry Price"] = f"${estimates.get('debit', 0):.2f}"
                    exec_data["Max Profit"] = f"${estimates.get('est_max_profit', 0):.2f}"
                    exec_data["Max Risk"] = f"${estimates.get('debit', 0):.2f}"
                    exec_data["Strike Price"] = f"${ctx.price:.2f}"  # ATM for calendars

            elif pd.name in ["leaps", "oversold_bounce"]:
                # Long call details
                exec_data["Strike Price"] = f"${ctx.price * 0.95:.2f}"  # Slightly ITM
                exec_data["Entry Price"] = f"${ctx.price * 0.08:.2f}"  # Rough premium estimate
                exec_data["Max Profit"] = "Unlimited"
                exec_data["Max Risk"] = exec_data["Entry Price"]

            elif pd.name in ["cash_secured_put"]:
                # CSP details
                exec_data["Strike Price"] = f"${ctx.price * 0.95:.2f}"  # 5% OTM
                exec_data["Entry Price"] = f"${ctx.price * 0.03:.2f}"  # Rough premium
                exec_data["Max Profit"] = exec_data["Entry Price"]
                exec_data["Max Risk"] = f"${ctx.price * 0.92:.2f}"  # Strike minus premium

            # Set expiration based on strategy
            dte_window = details.get("dte_window", (30, 45))
            if isinstance(dte_window, tuple):
                avg_dte = (dte_window[0] + dte_window[1]) // 2
                from datetime import datetime, timedelta
                exp_date = datetime.now() + timedelta(days=avg_dte)
                exec_data["Expiration"] = exp_date.strftime("%b %d, %Y")

            # Calculate target and stop based on strategy type
            if "credit" in estimates and estimates.get("net_credit", 0) > 0:
                # For credit strategies, target is 50% of max profit
                max_profit = estimates.get("max_profit", 0)
                exec_data["Target Price"] = f"${max_profit * 0.5:.2f}"
                exec_data["Stop Loss"] = f"${max_profit * 1.5:.2f}"
            else:
                # For debit strategies, use price-based targets
                exec_data["Target Price"] = f"${ctx.price * 1.10:.2f}"  # 10% gain
                exec_data["Stop Loss"] = f"${ctx.price * 0.90:.2f}"   # 10% loss

        # Update all labels
        for label, value in exec_data.items():
            if label in self.exec_labels:
                self.exec_labels[label].config(text=value)

    def _analysis_complete(self):
        """Called when analysis is complete"""
        self.refresh_btn.config(state=tk.NORMAL, text="Refresh")

    def _show_error(self, message: str):
        """Show error message"""
        self.status_label.config(text="Error", foreground="red")
        messagebox.showerror("Analysis Error", message)


class BestStrategyDashboard(tk.Tk):
    """Main dashboard application with stock tabs"""

    def __init__(self):
        super().__init__()
        self.title("AI-Powered Daily Stock Investment Planning System - Erica's Strategies")
        self.geometry("1200x800")
        self.configure(bg="#f0f0f0")

        # Stock symbols to analyze
        self.symbols = ["AMD", "NVDA", "GOOGL", "AAPL", "AMZN"]
        self.stock_tabs = {}

        self._build_ui()
        self._auto_refresh_all()

    def _build_ui(self):
        # Header
        header = ttk.Frame(self)
        header.pack(fill=tk.X, padx=10, pady=5)

        title_label = ttk.Label(header, text="Best Strategy Dashboard", font=("Arial", 18, "bold"))
        title_label.pack(side=tk.LEFT)

        # Control buttons
        controls = ttk.Frame(header)
        controls.pack(side=tk.RIGHT)

        ttk.Button(controls, text="Refresh All", command=self._refresh_all_tabs).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls, text="Export Report", command=self._export_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls, text="AI Assistant", command=self._open_ai_assistant).pack(side=tk.LEFT, padx=5)

        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(controls, text="Auto Refresh", variable=self.auto_refresh_var).pack(side=tk.LEFT, padx=5)

        # Status bar
        status_frame = ttk.Frame(header)
        status_frame.pack(side=tk.RIGHT, padx=20)

        self.status_label = ttk.Label(status_frame, text="Ready", foreground="green")
        self.status_label.pack()

        # Main container with paned window for resizable layout
        main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Left side: Stock analysis tabs
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=3)

        # Notebook for stock tabs
        self.notebook = ttk.Notebook(left_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs for each stock
        for symbol in self.symbols:
            tab = StockTab(self.notebook, symbol)
            self.stock_tabs[symbol] = tab

        # Right side: AI Assistant chat
        self.ai_frame = ttk.Frame(main_paned)
        main_paned.add(self.ai_frame, weight=1)

        self._build_ai_assistant()

    def _refresh_all_tabs(self):
        """Refresh analysis for all stock tabs"""
        self.status_label.config(text="Refreshing all tabs...", foreground="orange")

        for symbol, tab in self.stock_tabs.items():
            tab._refresh_analysis()

        # Update status after a delay
        self.after(2000, lambda: self.status_label.config(text="All tabs refreshed", foreground="green"))

    def _auto_refresh_all(self):
        """Auto-refresh all tabs on startup"""
        if self.auto_refresh_var.get():
            # Delay the refresh to ensure UI is fully loaded
            self.after(1000, self._refresh_all_tabs)

    def _build_ai_assistant(self):
        """Build the AI assistant chat interface"""
        # AI Assistant header
        ai_header = ttk.Frame(self.ai_frame)
        ai_header.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(ai_header, text="AI Assistant", font=("Arial", 14, "bold")).pack(side=tk.LEFT)

        # Chat history
        chat_frame = ttk.LabelFrame(self.ai_frame, text="Chat")
        chat_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.chat_history = ScrolledText(chat_frame, height=20, wrap=tk.WORD, font=("Arial", 9))
        self.chat_history.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.chat_history.configure(state=tk.DISABLED)

        # Input area
        input_frame = ttk.Frame(self.ai_frame)
        input_frame.pack(fill=tk.X, padx=5, pady=5)

        self.chat_input = tk.Text(input_frame, height=3, wrap=tk.WORD, font=("Arial", 9))
        self.chat_input.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # Buttons
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Send", command=self._send_chat_message).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Clear", command=self._clear_chat).pack(side=tk.RIGHT)

        # Quick action buttons
        quick_frame = ttk.LabelFrame(self.ai_frame, text="Quick Actions")
        quick_frame.pack(fill=tk.X, padx=5, pady=5)

        quick_buttons = [
            ("Explain Current Strategy", self._explain_current_strategy),
            ("Risk Assessment", self._risk_assessment),
            ("Market Summary", self._market_summary),
            ("Compare Strategies", self._compare_strategies)
        ]

        for i, (text, command) in enumerate(quick_buttons):
            row, col = i // 2, i % 2
            ttk.Button(quick_frame, text=text, command=command).grid(row=row, column=col, padx=2, pady=2, sticky=tk.EW)

        quick_frame.columnconfigure(0, weight=1)
        quick_frame.columnconfigure(1, weight=1)

        # Initialize chat
        self._add_chat_message("AI Assistant", "Hello! I'm here to help you understand the trading strategies and analysis. Ask me anything about the current recommendations!")

    def _add_chat_message(self, sender: str, message: str):
        """Add a message to the chat history"""
        self.chat_history.configure(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M")
        self.chat_history.insert(tk.END, f"[{timestamp}] {sender}: {message}\n\n")
        self.chat_history.configure(state=tk.DISABLED)
        self.chat_history.see(tk.END)

    def _send_chat_message(self):
        """Send user message and get AI response"""
        user_message = self.chat_input.get("1.0", tk.END).strip()
        if not user_message:
            return

        # Add user message
        self._add_chat_message("You", user_message)
        self.chat_input.delete("1.0", tk.END)

        # Get AI response
        self._get_ai_response(user_message)

    def _get_ai_response(self, user_message: str):
        """Get AI response to user message"""
        # Get current tab analysis for context
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        context = self._get_current_analysis_context(current_tab)

        # Simple rule-based responses for now (can be enhanced with OpenAI later)
        response = self._generate_ai_response(user_message, current_tab, context)
        self._add_chat_message("AI Assistant", response)

    def _generate_ai_response(self, message: str, current_symbol: str, context: dict) -> str:
        """Generate AI response based on message and context"""
        message_lower = message.lower()

        if any(word in message_lower for word in ["strategy", "recommend", "what should"]):
            if context and context.get("primary_decision"):
                pd = context["primary_decision"]
                return f"For {current_symbol}, I recommend the {pd.name.replace('_', ' ').title()} strategy with {context.get('primary_score', 0):.1%} confidence. {pd.rationale}"
            else:
                return f"I'm still analyzing {current_symbol}. Please refresh the tab to get the latest strategy recommendation."

        elif any(word in message_lower for word in ["risk", "loss", "danger"]):
            return f"For {current_symbol}, key risks include market volatility, earnings surprises, and sector rotation. Always use proper position sizing and stop losses."

        elif any(word in message_lower for word in ["price", "target", "entry"]):
            if context and context.get("price"):
                return f"{current_symbol} is currently trading at ${context['price']:.2f}. Check the Execution Details section for specific entry points and targets."
            else:
                return f"I need current price data for {current_symbol}. Please refresh the analysis."

        elif any(word in message_lower for word in ["rsi", "technical", "trend"]):
            if context:
                rsi = context.get("rsi")
                trend = context.get("trend")
                return f"{current_symbol} shows {trend} trend with RSI at {rsi:.1f}. {'Oversold conditions suggest potential bounce' if rsi < 30 else 'Overbought conditions suggest caution' if rsi > 70 else 'Neutral momentum conditions'}."
            else:
                return "I need technical analysis data. Please refresh the current tab."

        elif any(word in message_lower for word in ["compare", "vs", "versus", "better"]):
            return "I can help compare strategies across different stocks. Each stock has unique technical conditions that drive different strategy recommendations."

        else:
            return f"I can help you understand the {current_symbol} analysis, explain strategies, assess risks, or compare options. What specific aspect would you like to explore?"

    def _get_current_analysis_context(self, symbol: str) -> dict:
        """Get analysis context for the current symbol"""
        if symbol in self.stock_tabs:
            tab = self.stock_tabs[symbol]
            if tab.analysis_data:
                result = tab.analysis_data['result']
                ctx = tab.analysis_data['context']
                return {
                    'primary_decision': result.get('primary_decision'),
                    'primary_score': result.get('primary_score', 0),
                    'price': ctx.price,
                    'rsi': ctx.rsi,
                    'trend': ctx.trend,
                    'ivr': ctx.ivr
                }
        return {}

    def _clear_chat(self):
        """Clear chat history"""
        self.chat_history.configure(state=tk.NORMAL)
        self.chat_history.delete("1.0", tk.END)
        self.chat_history.configure(state=tk.DISABLED)
        self._add_chat_message("AI Assistant", "Chat cleared. How can I help you with your trading analysis?")

    def _explain_current_strategy(self):
        """Explain the current tab's strategy"""
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        context = self._get_current_analysis_context(current_tab)

        if context and context.get("primary_decision"):
            pd = context["primary_decision"]
            message = f"The recommended strategy for {current_tab} is {pd.name.replace('_', ' ').title()}. This strategy is suitable because: {pd.rationale}"
        else:
            message = f"No strategy analysis available for {current_tab}. Please refresh the tab first."

        self._add_chat_message("AI Assistant", message)

    def _risk_assessment(self):
        """Provide risk assessment for current symbol"""
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        message = f"Risk assessment for {current_tab}: Monitor volatility, earnings dates, and sector trends. Use proper position sizing (typically 1-3% of account per trade) and set stop losses at key technical levels."
        self._add_chat_message("AI Assistant", message)

    def _market_summary(self):
        """Provide market summary"""
        message = "Current market environment shows mixed conditions across tech stocks. Focus on individual technical setups rather than broad market timing. Each stock has unique catalysts and risk factors."
        self._add_chat_message("AI Assistant", message)

    def _compare_strategies(self):
        """Compare strategies across stocks"""
        strategies = []
        for symbol, tab in self.stock_tabs.items():
            if tab.analysis_data:
                pd = tab.analysis_data['result'].get('primary_decision')
                if pd:
                    strategies.append(f"{symbol}: {pd.name.replace('_', ' ').title()}")

        if strategies:
            message = "Current strategy recommendations:\n" + "\n".join(strategies)
        else:
            message = "No strategy data available. Please refresh all tabs first."

        self._add_chat_message("AI Assistant", message)

    def _open_ai_assistant(self):
        """Toggle AI assistant visibility"""
        # This could expand/collapse the AI panel or open in a separate window
        messagebox.showinfo("AI Assistant", "AI Assistant is already visible in the right panel!")

    def _export_report(self):
        """Export current analysis to HTML report"""
        messagebox.showinfo("Export", "HTML export functionality will be implemented")


def main():
    # Set API key
    os.environ["FMP_API_KEY"] = FMP_API_KEY

    app = BestStrategyDashboard()
    app.mainloop()


if __name__ == "__main__":
    main()
