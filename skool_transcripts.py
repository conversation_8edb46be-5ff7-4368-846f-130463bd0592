"""
Skool Transcript Fetcher (local, **no credentials shared**)
===========================================================

Goal
----
Fetch transcripts from a Skool classroom you can access *in your own browser session*.
- Works for **YouTube embeds** (public/unlisted) via API, or by opening YouTube and using the
  **Show transcript** panel (in your logged-in browser session) when the API path fails.
- **Best-effort** for **Vimeo**: captures any caption **.vtt** tracks the player loads and converts
  them to text. (Some videos do not expose captions; nothing to fetch in that case.)
- **No logins sent to me**. You run this locally. It uses a **persistent browser profile**
  so you can log in once and reuse.

What this does **not** do
-------------------------
- It does **not** download video or bypass DRM.
- It respects site permissions—only scrapes content you can view while logged in.
- It may break if Skool/YouTube/Vimeo UI changes; I added multiple selectors and fallbacks.

Quick start
-----------
1) Install deps (Python 3.10+ recommended):
   pip install playwright youtube-transcript-api
   playwright install chromium

2) Run (first time visible so you can log in):
   python skool_transcripts.py \
       --classroom "https://www.skool.com/your-community/classroom/abc123" \
       --out transcripts \
       --headful

   - A Chromium window opens. If redirected to Skool login, sign in. After login loads the classroom page,
     the script will enumerate lessons and begin fetching transcripts.
   - Next runs can be **headless** and reuse the saved profile automatically (omit --headful).

3) Outputs
   - One text file per lesson: `transcripts/<index>__<slugified-title>.txt`
   - Also CSV manifest: `transcripts/_manifest.csv` with lesson URL, title, platform, video ID.

CLI
---
python skool_transcripts.py --classroom <url> --out <dir> [--headful] [--max <N>] [--timeout 25]

- `--classroom`  Skool classroom URL (the page listing lessons)
- `--out`        Output directory
- `--headful`    Show the browser (default headless)
- `--max`        Limit number of lessons for a trial run
- `--timeout`    Seconds to wait for each page load step

"""
from __future__ import annotations
import os, re, csv, sys, time, math, pathlib, argparse, html
from dataclasses import dataclass
from typing import List, Optional, Dict, Tuple

from playwright.sync_api import sync_playwright, TimeoutError as PWTimeoutError

# Optional dependency for public YouTube transcripts
try:
    from youtube_transcript_api import YouTubeTranscriptApi, TranscriptsDisabled, NoTranscriptFound
    YT_OK = True
except Exception:
    YT_OK = False

# --------------------------- Utilities ---------------------------

def slug(s: str, maxlen: int = 80) -> str:
    s = html.unescape(s).strip().lower()
    s = re.sub(r"[^a-z0-9]+", "-", s)
    s = re.sub(r"-+", "-", s).strip("-")
    return s[:maxlen] or "untitled"

# Support standard, mobile, and nocookie YouTube embed/watch URLs
YOUTUBE_ID_RX = re.compile(
    r"(?:youtu\.be/|(?:www\.)?youtube(?:-nocookie)?\.com/(?:watch\?v=|embed/|shorts/))([A-Za-z0-9_-]{11})"
)

def extract_video_id(url_or_id: str) -> Optional[str]:
    s = url_or_id.strip()
    if re.fullmatch(r"[A-Za-z0-9_-]{11}", s):
        return s
    m = YOUTUBE_ID_RX.search(s)
    if m:
        return m.group(1)
    # Try v= param
    try:
        from urllib.parse import urlparse, parse_qs
        v = parse_qs(urlparse(s).query).get("v", [None])[0]
        if v and re.fullmatch(r"[A-Za-z0-9_-]{11}", v):
            return v
    except Exception:
        pass
    return None

# --------------------------- YouTube transcript paths ---------------------------

def fetch_youtube_transcript_api(video_id: str) -> Optional[str]:
    if not YT_OK:
        return None
    try:
        tlist = YouTubeTranscriptApi.list_transcripts(video_id)
        try:
            tr = tlist.find_transcript(["en","en-US","en-GB"])
        except Exception:
            try:
                tr = tlist.find_generated_transcript(["en","en-US","en-GB"])
            except Exception:
                tr = next(iter(tlist), None)
                if tr and 'en' not in getattr(tr, 'language_code', ''):
                    tr = tr.translate('en')
        if not tr:
            return None
        items = tr.fetch()
        text = " ".join([i.get('text','').strip() for i in items if i.get('text')])
        return re.sub(r"\s+", " ", text).strip() or None
    except (TranscriptsDisabled, NoTranscriptFound):
        return None
    except Exception:
        return None

# --------------------------- Data structures ---------------------------
@dataclass
class Lesson:
    title: str
    url: str
    platform: str  # 'youtube' | 'vimeo' | 'unknown'
    video_id: Optional[str]

# --------------------------- Core scraping ---------------------------

def find_lessons_on_classroom(page, timeout: int = 25) -> List[str]:
    # Grab all links that look like lesson pages under the classroom
    # Avoid strict networkidle to be resilient to dynamic content
    try:
        page.wait_for_load_state("domcontentloaded", timeout=timeout*1000)
    except Exception:
        pass
    # Skool uses anchors; collect unique hrefs containing '/classroom/' but not the listing root
    from urllib.parse import urljoin
    hrefs = set()
    for a in page.locator("a[href*='/classroom/']").all():
        try:
            href = a.get_attribute("href") or ""
            if href and "/classroom/" in href and not href.rstrip("/").endswith("/classroom"):
                abs_url = urljoin(page.url, href)
                if abs_url.startswith("http"):
                    hrefs.add(abs_url)
        except Exception:
            continue
    return sorted(hrefs)


def detect_embed(page) -> Tuple[str, Optional[str]]:
    """Return (platform, id) for first embed found on the lesson page."""
    # Try YouTube
    for frame in page.frames:
        u = (frame.url or "")
        if "youtube.com" in u or "youtu.be" in u:
            vid = extract_video_id(u)
            if not vid:
                # Sometimes the frame URL is about:blank; try the iframe element src
                try:
                    el = frame.frame_element()
                    src = el.get_attribute("src") or ""
                    vid = extract_video_id(src)
                except Exception:
                    pass
            return ("youtube", vid)
        if "player.vimeo.com" in u:
            # Vimeo id often is in the path /video/<id>
            m = re.search(r"/video/(\d+)", u)
            return ("vimeo", m.group(1) if m else None)
    # Try iframes directly
    for el in page.locator("iframe").all():
        src = el.get_attribute("src") or ""
        if "youtube" in src:
            return ("youtube", extract_video_id(src))
        if "vimeo" in src:
            m = re.search(r"/video/(\d+)", src)
            return ("vimeo", m.group(1) if m else None)
    return ("unknown", None)


def open_youtube_and_copy_transcript(context, video_id: str, timeout: int = 25) -> Optional[str]:
    """Open YouTube watch page and extract transcript via UI (logged-in session)."""
    page = context.new_page()
    try:
        page.goto(f"https://www.youtube.com/watch?v={video_id}", timeout=timeout*1000)
        page.wait_for_load_state("networkidle")
        # New UI: Transcript can be a direct button or under the More actions ("...") menu.
        # Try direct button first
        found = False
        for sel in [
            "button[aria-label*='Show transcript']",
            "tp-yt-paper-item:has-text('Show transcript')",
            "ytd-button-renderer:has-text('Transcript')",
        ]:
            try:
                if page.locator(sel).first.is_visible():
                    page.locator(sel).first.click()
                    found = True
                    break
            except Exception:
                pass
        if not found:
            # Open More actions menu
            try:
                page.get_by_role("button", name=re.compile("More actions|Actions|...")).click()
                time.sleep(0.5)
                page.get_by_text("Show transcript", exact=False).first.click()
                found = True
            except Exception:
                pass
        if not found:
            return None
        # Wait for transcript panel and collect lines
        page.wait_for_selector("ytd-transcript-segment-renderer #segment-text, ytd-transcript-segment-renderer yt-formatted-string", timeout=timeout*1000)
        texts = page.locator("ytd-transcript-segment-renderer #segment-text, ytd-transcript-segment-renderer yt-formatted-string").all_inner_texts()
        text = " ".join([t.strip() for t in texts if t.strip()])
        return re.sub(r"\s+", " ", text).strip() or None
    except PWTimeoutError:
        return None
    finally:
        try:
            page.close()
        except Exception:
            pass


def capture_vimeo_vtt(context, lesson_url: str, timeout: int = 25) -> Optional[str]:
    """Best-effort: open the lesson page again and watch for .vtt caption track loads."""
    page = context.new_page()
    vtt_texts: List[str] = []
    try:
        def handle_response(resp):
            try:
                url = resp.url
                if url.endswith('.vtt'):
                    body = resp.text()
                    # Convert WebVTT to plain text (drop timing lines)
                    lines = []
                    for line in body.splitlines():
                        if not line.strip():
                            continue
                        if re.match(r"\d{2}:\d{2}:\d{2}\.\d{3} --> ", line):
                            continue
                        if line.startswith("WEBVTT"):
                            continue
                        lines.append(line)
                    vtt_texts.append(" ".join(lines))
            except Exception:
                pass
        page.on("response", handle_response)
        page.goto(lesson_url, timeout=timeout*1000)
        page.wait_for_load_state("networkidle")
        # Give player time to load tracks
        time.sleep(6)
        text = " ".join([t.strip() for t in vtt_texts if t.strip()])
        return re.sub(r"\s+", " ", text).strip() or None
    except PWTimeoutError:
        return None
    finally:
        try:
            page.close()
        except Exception:
            pass

# --------------------------- Main workflow ---------------------------

def fetch_classroom(classroom_url: str, outdir: str, headful: bool, max_items: Optional[int], timeout: int = 25, browser: str = "chromium", single: bool = False) -> None:
    out = pathlib.Path(outdir); out.mkdir(parents=True, exist_ok=True)
    manifest_path = out / "_manifest.csv"
    profile_dir = "./skool_profile"
    channel = None
    if browser.lower() == "edge":
        profile_dir = "./skool_profile_edge"
        channel = "msedge"
    elif browser.lower() == "chrome":
        profile_dir = "./skool_profile_chrome"
        channel = "chrome"
    first_time_profile = not pathlib.Path(profile_dir).exists()

    with sync_playwright() as p:
        context = p.chromium.launch_persistent_context(
            user_data_dir=profile_dir,
            headless=not headful,
            channel=channel,
            args=["--disable-features=AutomationControlled"],
        )
        page = context.new_page()
        page.goto(classroom_url, timeout=timeout*1000)
        try:
            page.wait_for_load_state("load", timeout=timeout*1000)
        except PWTimeoutError:
            pass

        # If we got bounced to login, let the user log in manually (headful recommended on first run)
        if "login" in (page.url.lower()):
            if not headful:
                print("[!] You appear to be on a login page. Re-run with --headful to sign in once.")
                context.close(); return
            print("[i] Please log in; the script will continue after the classroom loads…")
            # Wait up to 5 minutes for user to log in and reach classroom
            for _ in range(300):
                time.sleep(1)
                if "classroom" in page.url:
                    break

        lesson_links = [] if single else find_lessons_on_classroom(page, timeout=timeout)
        if not lesson_links:
            # Interactive wait: allow user to log in or navigate, then retry discovery
            print("[i] No lesson list detected yet. You may need to log in or open the lesson page.\n"
                  "    Keeping the browser open—please complete login/navigation.\n"
                  "    I'll retry detection for up to 5 minutes…")
            deadline = time.time() + 300
            found_mode = None  # 'list' or 'single'
            single_info: Tuple[str, str, Optional[str]] | None = None  # (title, platform, vid)
            while time.time() < deadline:
                try:
                    # Try to detect list
                    if not single:
                        lesson_links = find_lessons_on_classroom(page, timeout=timeout)
                    if lesson_links:
                        found_mode = 'list'
                        break
                    # Try to detect a single lesson embed on current page
                    platform, vid = detect_embed(page)
                    if platform in ("youtube", "vimeo"):
                        title = page.title() or "Lesson"
                        single_info = (title, platform, vid)
                        found_mode = 'single'
                        break
                except Exception:
                    pass
                time.sleep(2)

            # If still nothing after waiting, exit gracefully
            if not found_mode:
                print("[!] Still no lessons or embed found after waiting. Leaving the browser open for you to investigate.")
                context.close(); return

            if found_mode == 'single' and single_info:
                title, platform, vid = single_info
                text: Optional[str] = None
                if platform == "youtube" and vid:
                    text = fetch_youtube_transcript_api(vid)
                    if not text:
                        text = open_youtube_and_copy_transcript(context, vid, timeout=timeout)
                elif platform == "vimeo":
                    text = capture_vimeo_vtt(context, page.url, timeout=timeout)

                new_manifest = not manifest_path.exists()
                mf = open(manifest_path, "a", newline="", encoding="utf-8")
                writer = csv.writer(mf)
                if new_manifest:
                    writer.writerow(["index","title","platform","video_id","lesson_url","file"])
                fname = None
                if text:
                    fname = f"{1:03d}__{slug(title)}.txt"
                    (out / fname).write_text(text, encoding="utf-8")
                    print(f"[ok] 001 {title}  -> {fname}  ({len(text)} chars)")
                else:
                    print("[--] 001 {0}  (no transcript found)".format(title))
                writer.writerow([1, title, platform, vid or "", page.url, fname or ""])
                mf.close()
                context.close(); return
        if max_items:
            lesson_links = lesson_links[:max_items]

        # Prepare manifest
        new_manifest = not manifest_path.exists()
        mf = open(manifest_path, "a", newline="", encoding="utf-8")
        writer = csv.writer(mf)
        if new_manifest:
            writer.writerow(["index","title","platform","video_id","lesson_url","file"])

        for idx, url in enumerate(lesson_links, 1):
            try:
                page.goto(url, timeout=timeout*1000)
                try:
                    page.wait_for_load_state("networkidle", timeout=timeout*1000)
                except PWTimeoutError:
                    pass
                title = page.title() or f"Lesson {idx}"
                platform, vid = detect_embed(page)
                text: Optional[str] = None
                if platform == "youtube" and vid:
                    text = fetch_youtube_transcript_api(vid)
                    if not text:
                        text = open_youtube_and_copy_transcript(context, vid, timeout=timeout)
                elif platform == "vimeo":
                    text = capture_vimeo_vtt(context, url, timeout=timeout)
                # Save if we have text
                fname = None
                if text:
                    fname = f"{idx:03d}__{slug(title)}.txt"
                    (out / fname).write_text(text, encoding="utf-8")
                    print(f"[ok] {idx:03d} {title}  -> {fname}  ({len(text)} chars)")
                else:
                    print(f"[--] {idx:03d} {title}  (no transcript found)")
                writer.writerow([idx, title, platform, vid or "", url, fname or ""])
            except Exception as e:
                print(f"[err] {idx:03d} {url}  ({e})")
        mf.close()
        context.close()

# --------------------------- CLI ---------------------------

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--classroom", required=True, help="Skool classroom URL (the listing page)")
    ap.add_argument("--out", default="transcripts", help="Output directory")
    ap.add_argument("--headful", action="store_true", help="Run with visible browser to handle login")
    ap.add_argument("--max", type=int, default=None, help="Limit number of lessons for a trial run")
    ap.add_argument("--timeout", type=int, default=25, help="Per-step timeout in seconds")
    ap.add_argument("--browser", choices=["chromium","edge","chrome"], default="chromium", help="Browser to use (Edge recommended on Windows)")
    ap.add_argument("--single", action="store_true", help="Treat the provided URL as a single lesson page")
    args = ap.parse_args()

    fetch_classroom(args.classroom, args.out, headful=args.headful, max_items=args.max, timeout=args.timeout, browser=args.browser, single=args.single)

if __name__ == "__main__":
    main()


